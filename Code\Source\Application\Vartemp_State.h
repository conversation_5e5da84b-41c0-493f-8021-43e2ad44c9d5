#ifndef VAR_CYCLE_H
#define VAR_CYCLE_H

#include <stddef.h>
#include "Parameter_TemperatureZone.h"
#include "Driver_DoorSwitch.h"
#include "Driver_AdSample.h"
#include "DisplayInterface.h"
#include "CoolingCycle.h"
#include "SystemTimerModule.h"
#include "Defrosting.h"
#include "Core_CallBackTimer.h"


#define U16_VARRIGHT_LEDOFF_TIME_MINUTE ((uint16_t)2 * 60)
#define U16_VARRIGHT_LEDON_TIME_SECOND ((uint16_t)3 * 60)
#define U16_OVERLOAD_TIME_MINUTE ((uint16_t)5 * 60)
#define U16_OVERLOAD_FRESHMEAT_ADJSUT_TEMP ((uint16_t)20)
#define U16_OVERLOAD_AQUATIC_ADJSUT_TEMP ((uint16_t)10)
#define U16_OVERLOAD_FRESHMEAT_TEMP ((uint16_t)500)
#define U16_OVERLOAD_AQUATIC_TEMP ((uint16_t)520)
#define U16_OVERLOAD_EXIT_TEMP ((uint16_t)490)

enum
{
    eVarControl_Stage_First = 0,
    eVarControl_Stage_Second,
    eVarControl_Stage_Third = 1,
    eVarControl_Stage_Max
};
typedef uint8_t VarControlStage_t;

void Var_Cycle_Init(void);

void Var_Cycle_Exit(void);

void UpdateVartempOffset(void);

uint16_t Get_Var_On(void);

uint16_t Get_Var_Off(void);

VarControlStage_t GetVarControlStage(void);

bool IsVarControlOverloadProtect(void);

bool IsVarControlFreezeProtect(void);

bool IsVarControlThawControl(void);

#endif
