/*!
 * @file
 * @brief door switch detection and alarm.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Driver_DoorSwitch.h"
#include "Adpt_GPIO.h"
#include "Driver_GradualLamp.h"
#include "IO_Device.h"
#include "DisplayInterface.h"
#include "Core_Types.h"
#include "ResolverDevice.h"
#include "Driver_Emulator.h"
#include "Sbus_Display.h"
#include "Driver_SingleDamper.h"
#include "SystemTimerModule.h"
#include "Sbus_IceMaker.h"
#include "ParameterManager.h"
#include "Driver_CompFrequency.h"

static bool b_RefLeftDoorOpenLamp = false;
static bool b_RefRightDoorOpenLamp = false;
static bool b_FrzLeftDoorOpenLamp = false;
static bool b_FrzRightDoorOpenLamp = false;
static DoorState_st ary_DoorState[(uint8_t)DOOR_MAX_NUMBER];
static uint8_t u8_FrzFanDelayTime;
static uint8_t u8_RefFanDelayTime;
static uint8_t u8_CoolFanDelayTime;
static uint8_t u8_VerticalBeamHeaterDelayTime;
static uint8_t u8_CompFrequencyDelayTime;
static uint8_t u8_CompFrequencyCloseDelayTime;

static void Judge_RefLeftDoorState(void);
static void Judge_RefRightDoorState(void);
static void Judge_FrzLeftDoorState(void);
static void Judge_FrzRightDoorState(void);
static void Handle_RefDoorState(void);
static void Handle_FrzDoorState(void);
static void Judge_AllDoorState(void);
static void Cal_AllDoorsTimeSecond(void);
static void Judge_AllDoorsAlarmAndSwitchError(void);
static void Deal_DoorOpenAlarm(void);
static void Handle_DoorOpen(void);
static void DoorSwitch_CtrlDisplay(void);
static void DoorSwitch_CtrlLamp(void);

static DoorSwitchState_st ary_DoorSwitchState[(uint8_t)DOOR_REF] = {
    { Judge_RefLeftDoorState, Handle_RefDoorState, IO_LEVEL_HIGH },
    { Judge_RefRightDoorState, Handle_RefDoorState, IO_LEVEL_HIGH },
    { Judge_FrzLeftDoorState, Handle_FrzDoorState, IO_LEVEL_HIGH },
    { Judge_FrzRightDoorState, Handle_FrzDoorState, IO_LEVEL_HIGH }
};

static void Judge_RefLeftDoorState(void)
{
    if((uint8_t)IO_REF_LEFT_DOOR_IN == ary_DoorSwitchState[(uint8_t)DOOR_REF_LEFT].u8_DoorOpenSwitchState)
    {
        ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorSwitchState = DOOR_OPEN;
    }
    else
    {
        ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorSwitchState = DOOR_CLOSE;
    }
}

static void Judge_RefRightDoorState(void)
{
    if((uint8_t)IO_REF_RIGHT_DOOR_IN == ary_DoorSwitchState[(uint8_t)DOOR_REF_RIGHT].u8_DoorOpenSwitchState)
    {
        ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorSwitchState = DOOR_OPEN;
    }
    else
    {
        ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorSwitchState = DOOR_CLOSE;
    }
}

static void Judge_FrzLeftDoorState(void)
{
    if((uint8_t)IO_FRZ_LEFT_DOOR_IN == ary_DoorSwitchState[(uint8_t)DOOR_FRZ_LEFT].u8_DoorOpenSwitchState)
    {
        ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorSwitchState = DOOR_OPEN;
    }
    else
    {
        ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorSwitchState = DOOR_CLOSE;
    }
}

static void Judge_FrzRightDoorState(void)
{
    if((uint8_t)IO_FRZ_RIGHT_DOOR_IN == ary_DoorSwitchState[(uint8_t)DOOR_FRZ_RIGHT].u8_DoorOpenSwitchState)
    {
        ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorSwitchState = DOOR_OPEN;
    }
    else
    {
        ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorSwitchState = DOOR_CLOSE;
    }
}

static void Handle_RefDoorState(void)
{
    if((DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorState) ||
        (DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorState))
    {
        ary_DoorState[(uint8_t)DOOR_REF].f_DoorSwitchState = DOOR_OPEN;
        ary_DoorState[(uint8_t)DOOR_REF].f_DoorState = DOOR_OPEN;
    }
    else
    {
        if(ary_DoorState[(uint8_t)DOOR_REF].f_DoorState == DOOR_OPEN)
        {
            ary_DoorState[(uint8_t)DOOR_REF].u8_DoorOpenCloseTotalCounter++;
        }
        ary_DoorState[(uint8_t)DOOR_REF].f_DoorSwitchState = DOOR_CLOSE;
        ary_DoorState[(uint8_t)DOOR_REF].f_DoorState = DOOR_CLOSE;
    }
}

static void Handle_FrzDoorState(void)
{
    if((DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorState) ||
        (DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorState))
    {
        ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorSwitchState = DOOR_OPEN;
        ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorState = DOOR_OPEN;
    }
    else
    {
        if(ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorState == DOOR_OPEN)
        {
            ary_DoorState[(uint8_t)DOOR_FRZ].u8_DoorOpenCloseTotalCounter++;
        }
        ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorSwitchState = DOOR_CLOSE;
        ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorState = DOOR_CLOSE;
    }
}

static void Judge_AllDoorState(void)
{
    if((DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_REF].f_DoorState) ||
        (DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorState))
    {
        ary_DoorState[(uint8_t)DOOR_ALL].f_DoorSwitchState = DOOR_OPEN;

        ary_DoorState[(uint8_t)DOOR_ALL].f_DoorState = DOOR_OPEN;
    }
    else
    {
        if(ary_DoorState[(uint8_t)DOOR_ALL].f_DoorState == DOOR_OPEN)
        {
            ary_DoorState[(uint8_t)DOOR_ALL].u8_DoorOpenCloseTotalCounter++;
        }
        ary_DoorState[(uint8_t)DOOR_ALL].f_DoorSwitchState = DOOR_CLOSE;

        ary_DoorState[(uint8_t)DOOR_ALL].f_DoorState = DOOR_CLOSE;
    }
}

void Update_AllDoorsSwitchState(void)
{
    uint8_t u8_index = 0;
    DoorState_st *p_door_state = (DoorState_st *)NULL;
    DoorSwitchState_st *p_door_switch_state = (DoorSwitchState_st *)NULL;

    p_door_state = ary_DoorState;
    p_door_switch_state = ary_DoorSwitchState;

    for(u8_index = 0; u8_index < (uint8_t)DOOR_REF; u8_index++)
    {
        if(NULL == p_door_switch_state->p_JudgeDoorSwitchState)
        {
            p_door_state->f_DoorSwitchState = DOOR_CLOSE;
        }
        else
        {
            p_door_switch_state->p_JudgeDoorSwitchState();
        }

        if(p_door_state->f_DoorState == p_door_state->f_DoorSwitchState)
        {
            p_door_state->u8_JudgeDoorState10MsCounter = 0;
        }
        else
        {
            p_door_state->u8_JudgeDoorState10MsCounter++;
            if(p_door_state->u8_JudgeDoorState10MsCounter >= U8_ANTI_SHAKE_TIMER)
            {
                if(DOOR_CLOSE == p_door_state->f_DoorState)
                {
                    // 开门
                    p_door_state->f_DoorState = DOOR_OPEN;
                    p_door_state->f_DoorOpenClose = false;
                }
                else
                {
                    // 关门
                    p_door_state->f_DoorState = DOOR_CLOSE;
                    p_door_state->f_DoorOpenClose = true;
                    p_door_state->u8_DoorOpenCloseTotalCounter++;
                }

                if(p_door_switch_state->p_HandleDoorSwitchState != NULL)
                {
                    p_door_switch_state->p_HandleDoorSwitchState();
                }
            }
        }

        p_door_state++;
        p_door_switch_state++;
    }

    Judge_AllDoorState();
    DoorSwitch_CtrlLamp();
}

static void Cal_AllDoorsTimeSecond(void)
{
    uint8_t u8_index = 0;
    DoorState_st *p_door_state = (DoorState_st *)NULL;

    p_door_state = ary_DoorState;

    for(u8_index = 0; u8_index < (uint8_t)DOOR_MAX_NUMBER; u8_index++)
    {
        if(DOOR_OPEN == p_door_state->f_DoorState)
        {
            // 开门
            p_door_state->u16_DoorCloseSecondCounter = 0;

            if(p_door_state->u16_DoorOpenSecondCounter < U16_MAX_UINT16)
            {
                p_door_state->u16_DoorOpenSecondCounter++;
            }

            if(p_door_state->u16_DoorOpenTotalSecondCounter < U16_MAX_UINT16)
            {
                p_door_state->u16_DoorOpenTotalSecondCounter++;
            }
        }
        else
        {
            // 关门
            p_door_state->u16_DoorOpenSecondCounter = 0;

            if(p_door_state->u16_DoorCloseSecondCounter < U16_MAX_UINT16)
            {
                p_door_state->u16_DoorCloseSecondCounter++;
            }
        }

        p_door_state++;
    }
}

static void Judge_AllDoorsAlarmAndSwitchError(void)
{
    uint8_t u8_index = 0;
    DoorState_st *p_door_state = (DoorState_st *)NULL;

    p_door_state = ary_DoorState;

    for(u8_index = 0; u8_index < (uint8_t)DOOR_MAX_NUMBER; u8_index++)
    {
        if(p_door_state->u16_DoorOpenSecondCounter < U16_OPEN_DOOR_ALARM_DELAY_TIME_SECOND)
        {
            p_door_state->f_DoorOpenAlarm = false;
            p_door_state->f_DoorOpenLamp = false;
            p_door_state->f_DoorSwitchError = false;
        }
        else if(p_door_state->u16_DoorOpenSecondCounter < U16_DOOR_OPEN_CLOSE_LAMP_TIME_SECOND)
        {
            p_door_state->f_DoorOpenAlarm = true;
            p_door_state->f_DoorOpenLamp = false;
            p_door_state->f_DoorSwitchError = false;
        }
        else if(p_door_state->u16_DoorOpenSecondCounter < U16_DOOR_OPEN_SWITCH_ERROR_TIME_SECOND)
        {
            p_door_state->f_DoorOpenAlarm = true;
            p_door_state->f_DoorOpenLamp = true;
            p_door_state->f_DoorSwitchError = false;
        }
        else
        {
            p_door_state->f_DoorOpenAlarm = true;
            p_door_state->f_DoorOpenLamp = true;
            p_door_state->f_DoorSwitchError = true;
        }

        if(p_door_state->u16_DoorOpenSecondCounter >= U16_DOOR_OPEN_DEFAULT_CLOSE_TIME_SECOND)
        {
            p_door_state->f_DoorDefaultClose = true;
        }
        else
        {
            p_door_state->f_DoorDefaultClose = false;
        }
        p_door_state++;
    }
}

static void Ctrl_FrzLeftLamp(bool b_ref_left_door_state, bool b_frz_Left_door_state)
{
    bool b_state = true;

    if((false == b_frz_Left_door_state) ||
        (true == b_ref_left_door_state))
    {
        b_state = false;
    }

    Vote_DeviceStatus_Immediate(FSM_OpenDoorControl, DEVICE_FrzLeftLamp, b_state);
}

static void Ctrl_FrzRightLamp(bool b_ref_right_door_state, bool b_frz_right_door_state)
{
    bool b_state = true;

    if((false == b_frz_right_door_state) ||
        (true == b_ref_right_door_state))
    {
        b_state = false;
    }

    Vote_DeviceStatus_Immediate(FSM_OpenDoorControl, DEVICE_FrzRightLamp, b_state);
}


static void DoorSwitch_CtrlLamp(void)
{
    static uint16_t frz_led_cnt = 0;

    if(((true == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorState) &&
           (false == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorOpenLamp)) ||
        ((true == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorState) &&
            (false == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorOpenLamp)))
    {
        Set_GradualLampState(REF_SURFACE_LAMP, true);
        Vote_DeviceStatus_Immediate(FSM_OpenDoorControl, DEVICE_VarLeftLamp, DS_On);
        Vote_DeviceStatus_Immediate(FSM_OpenDoorControl, DEVICE_VarRightLamp, DS_On);
    }
    else
    {
        Set_GradualLampState(REF_SURFACE_LAMP, false);
        Vote_DeviceStatus_Immediate(FSM_OpenDoorControl, DEVICE_VarLeftLamp, DS_DontCare);
        Vote_DeviceStatus_Immediate(FSM_OpenDoorControl, DEVICE_VarRightLamp, DS_DontCare);
    }

    if(((true == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorState) &&
           (false == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorOpenLamp)))
    {
        if(false == b_RefLeftDoorOpenLamp)
        {
            b_RefLeftDoorOpenLamp = true;
        }
    }
    else
    {
        if(true == b_RefLeftDoorOpenLamp)
        {
            b_RefLeftDoorOpenLamp = false;
        }
    }

    if(((true == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorState) &&
           (false == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorOpenLamp)))
    {
        if(false == b_RefRightDoorOpenLamp)
        {
            b_RefRightDoorOpenLamp = true;
        }
    }
    else
    {
        if(true == b_RefRightDoorOpenLamp)
        {
            b_RefRightDoorOpenLamp = false;
        }
    }

    if(((true == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorState) &&
           (false == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorOpenLamp)))
    {
        if(false == b_FrzLeftDoorOpenLamp)
        {
            b_FrzLeftDoorOpenLamp = true;
        }
    }
    else
    {
        if(true == b_FrzLeftDoorOpenLamp)
        {
            b_FrzLeftDoorOpenLamp = false;
        }
    }

    if(((true == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorState) &&
           (false == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorOpenLamp)))
    {
        if(false == b_FrzRightDoorOpenLamp)
        {
            b_FrzRightDoorOpenLamp = true;
        }
    }
    else
    {
        if(true == b_FrzRightDoorOpenLamp)
        {
            b_FrzRightDoorOpenLamp = false;
        }
    }

    if(false == ary_DoorState[(uint8_t)DOOR_ALL].f_DoorState)
    {
        b_RefLeftDoorOpenLamp = false;
        b_RefRightDoorOpenLamp = false;
        b_FrzLeftDoorOpenLamp = false;
        b_FrzRightDoorOpenLamp = false;
        if(frz_led_cnt > 0)
        {
            b_RefLeftDoorOpenLamp = true;
            b_RefRightDoorOpenLamp = true;
            b_FrzLeftDoorOpenLamp = true;
            b_FrzRightDoorOpenLamp = true;
            frz_led_cnt--;
        }
    }
    else
    {
        frz_led_cnt = U16_FRZLED_DELAY_TIMER_AFTER_CLOSE;
    }
    Ctrl_FrzLeftLamp(false, b_RefLeftDoorOpenLamp || b_RefRightDoorOpenLamp || b_FrzLeftDoorOpenLamp || b_FrzRightDoorOpenLamp);
    Ctrl_FrzRightLamp(false, b_RefLeftDoorOpenLamp || b_RefRightDoorOpenLamp || b_FrzLeftDoorOpenLamp || b_FrzRightDoorOpenLamp);
    
    if(b_RefLeftDoorOpenLamp || b_RefRightDoorOpenLamp ||
        b_FrzLeftDoorOpenLamp || b_FrzRightDoorOpenLamp)
    {
        Vote_DeviceStatus_Immediate(FSM_OpenDoorControl, DEVICE_VarDamper, DAMPER_FREEZED);
    }
    else
    {
        Vote_DeviceStatus(FSM_OpenDoorControl, DEVICE_VarDamper, DS_DontCare);
    }
}

static void DoorSwitch_CtrlRefFrzFan(void)
{
    if(true == ary_DoorState[(uint8_t)DOOR_REF].f_DoorState)
    {
        if(ary_DoorState[DOOR_REF].f_DoorDefaultClose == true)
        {
            if(u8_RefFanDelayTime > 0)
            {
                u8_RefFanDelayTime--;
                if(0 == u8_RefFanDelayTime)
                {
                    Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefFan, (int8_t)DS_DontCare);
                }
            }
            else
            {
                Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefFan, (int8_t)DS_DontCare);
            }
        }
        else
        {
            Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefFan, (int8_t)DS_Off);
            u8_RefFanDelayTime = U8_FAN_DELAY_TIMER_AFTER_DOOR_CLOSE;
        }
    }
    else
    {
        if(u8_RefFanDelayTime > 0)
        {
            u8_RefFanDelayTime--;
            if(0 == u8_RefFanDelayTime)
            {
                Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefFan, (int8_t)DS_DontCare);
            }
        }
        else
        {
            Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefFan, (int8_t)DS_DontCare);
        }
    }

    if(true == ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorState)
    {
        if(ary_DoorState[DOOR_FRZ].f_DoorDefaultClose == true)
        {
            if(u8_FrzFanDelayTime > 0)
            {
                u8_FrzFanDelayTime--;
                if(0 == u8_FrzFanDelayTime)
                {
                    Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_FrzFan, (int8_t)DS_DontCare);
                }
            }
            else
            {
                Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_FrzFan, (int8_t)DS_DontCare);
            }
        }
        else
        {
            Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_FrzFan, (int8_t)DS_Off);
            u8_FrzFanDelayTime = U8_FAN_DELAY_TIMER_AFTER_DOOR_CLOSE;
        }
    }
    else
    {
        if(u8_FrzFanDelayTime > 0)
        {
            u8_FrzFanDelayTime--;
            if(0 == u8_FrzFanDelayTime)
            {
                Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_FrzFan, (int8_t)DS_DontCare);
            }
        }
        else
        {
            Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_FrzFan, (int8_t)DS_DontCare);
        }
    }
}

static void DoorSwitch_CtrlCoolFan(void)
{
    if(true == ary_DoorState[(uint8_t)DOOR_ALL].f_DoorState)
    {
        if(ary_DoorState[DOOR_ALL].u16_DoorOpenSecondCounter > U16_DOOR_OPEN_COOL_FAN_TIME_SECOND)
        {
            u8_CoolFanDelayTime = 0;
            Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_CoolFan, (int8_t)DS_DontCare);
        }
        else
        {
            u8_CoolFanDelayTime = U8_FAN_DELAY_TIMER_AFTER_DOOR_CLOSE;
            Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_CoolFan, (int8_t)DS_Off);
        }
    }
    else
    {
        if(u8_CoolFanDelayTime > 0)
        {
            u8_CoolFanDelayTime--;
            if(0 == u8_CoolFanDelayTime)
            {
                Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_CoolFan, (int8_t)DS_DontCare);
            }
        }
        else
        {
            Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_CoolFan, (int8_t)DS_DontCare);
        }
    }
}


static void DoorSwitch_IonGenerator(void)
{
    if(DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_ALL].f_DoorState)
    {
        Vote_DeviceStatus(FSM_OpenDoorControl, DEVICE_IonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_OpenDoorControl, DEVICE_FrzIonGenerator, DS_Off);
    }
    else
    {
        Vote_DeviceStatus(FSM_OpenDoorControl, DEVICE_IonGenerator, DS_DontCare);
        Vote_DeviceStatus(FSM_OpenDoorControl, DEVICE_FrzIonGenerator, DS_DontCare);
    }
}

static void DoorSwitch_VerticalBeamHeater(void)
{
    if(true == ary_DoorState[(uint8_t)DOOR_REF].f_DoorState)
    {
        Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_VerticalBeamHeater, (int8_t)DS_Off);
        u8_VerticalBeamHeaterDelayTime = U8_VERTICALBEAMHEATER_DELAY_TIMER_AFTER_DOOR_CLOSE;
        if(ary_DoorState[DOOR_REF].f_DoorDefaultClose == true)
        {
            Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_VerticalBeamHeater, (int8_t)DS_DontCare);
        }
    }
    else
    {
        if(u8_VerticalBeamHeaterDelayTime > 0)
        {
            u8_VerticalBeamHeaterDelayTime--;
            if(0 == u8_VerticalBeamHeaterDelayTime)
            {
                Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_VerticalBeamHeater, (int8_t)DS_DontCare);
            }
        }
    }
}

static void DoorSwitch_CompFrequency(void)
{
    Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_Comp, (int8_t)DS_DontCare);
    if(true == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorState &&
        true == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorState &&
        (ary_DoorState[DOOR_FRZ_LEFT].f_DoorDefaultClose == false ||
        ary_DoorState[DOOR_FRZ_RIGHT].f_DoorDefaultClose == false))
    { 
        u8_CompFrequencyDelayTime = U8_COMPFREQUENCY_DELAY_TIMER_AFTER_DEFAULT_CLOSE;
        u8_CompFrequencyCloseDelayTime = U8_COMPFREQUENCY_DELAY_TIMER_AFTER_DOOR_CLOSE;
        if(Get_ResolvedDeviceStatusFSM((uint8_t)FSM_NormalControl, DEVICE_Comp) >= FREQ_130HZ)
        {
            Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_Comp, (int8_t)FREQ_118HZ);
        }
    }
    else if(false == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorState ||
            false == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorState)
    {
        if(u8_CompFrequencyCloseDelayTime > 0)
        {
            if(false == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorState && 
               false == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorState)
            {
                u8_CompFrequencyCloseDelayTime--;
            }
            else
            {
                u8_CompFrequencyCloseDelayTime = U8_COMPFREQUENCY_DELAY_TIMER_AFTER_DOOR_CLOSE;
            }

            if(Get_ResolvedDeviceStatusFSM((uint8_t)FSM_NormalControl, DEVICE_Comp) >= FREQ_130HZ)
            {
                Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_Comp, (int8_t)FREQ_118HZ);
            }
        }   
    }
    else
    {
        if(u8_CompFrequencyDelayTime > 0)
        {
            u8_CompFrequencyDelayTime--;
            if(Get_ResolvedDeviceStatusFSM((uint8_t)FSM_NormalControl, DEVICE_Comp) >= FREQ_130HZ)
            {
                Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_Comp, (int8_t)FREQ_118HZ);
            }
        }
    }
}

static void Handle_DoorOpen(void)
{
    DoorSwitch_CtrlRefFrzFan();
    DoorSwitch_CtrlCoolFan();
    DoorSwitch_IonGenerator();
    DoorSwitch_VerticalBeamHeater();
    DoorSwitch_CompFrequency();
}

void Handle_AllDoorsState(void)
{
    Cal_AllDoorsTimeSecond();
    Judge_AllDoorsAlarmAndSwitchError();
    Handle_DoorOpen();
}

__EMULATOR__FUNCITON uint16_t Get_DoorOpenTimeSecond(DoorTypeId_t typeId)
{
    uint16_t u16_doorOpenTimeSecond = 0;

    if(typeId < (DoorTypeId_t)DOOR_MAX_NUMBER)
    {
        u16_doorOpenTimeSecond = ary_DoorState[typeId].u16_DoorOpenTotalSecondCounter;
    }

    return u16_doorOpenTimeSecond;
}

__EMULATOR__FUNCITON uint8_t Get_DoorOpenCloseCounter(DoorTypeId_t typeId)
{
    uint8_t u8_doorOpenCloseCounter = 0;

    if(typeId < (DoorTypeId_t)DOOR_MAX_NUMBER)
    {
        u8_doorOpenCloseCounter = ary_DoorState[typeId].u8_DoorOpenCloseTotalCounter;
    }

    return u8_doorOpenCloseCounter;
}

__EMULATOR__FUNCITON void Clear_DoorOpenTimeSecond(DoorTypeId_t typeId)
{
    uint8_t u8_index = 0;
    uint8_t u8_indexend = 0;

    if(typeId < (DoorTypeId_t)DOOR_MAX_NUMBER)
    {
        ary_DoorState[typeId].u16_DoorOpenTotalSecondCounter = 0;

        if(DOOR_REF == typeId)
        {
            u8_index = (uint8_t)DOOR_REF_LEFT;
            u8_indexend = (uint8_t)DOOR_REF_RIGHT;
        }
        else if(DOOR_FRZ == typeId)
        {
            u8_index = (uint8_t)DOOR_FRZ_LEFT;
            u8_indexend = (uint8_t)DOOR_FRZ_RIGHT;
        }
        else if(DOOR_ALL == typeId)
        {
            u8_index = (uint8_t)DOOR_REF_LEFT;
            u8_indexend = (uint8_t)DOOR_ALL;
        }

        if(u8_index != u8_indexend)
        {
            for(; u8_index <= u8_indexend; u8_index++)
            {
                ary_DoorState[u8_index].u16_DoorOpenTotalSecondCounter = 0;
            }
        }
    }
}

bool Get_DoorSwitchState(DoorTypeId_t typeId)
{
    bool b_doorSwitchState = false;

    if(typeId < DOOR_MAX_NUMBER)
    {
        if(true == ary_DoorState[typeId].f_DoorState)
        {
            b_doorSwitchState = true;
        }
    }

    return b_doorSwitchState;
}

bool Get_DoorSwitchAlarmState(DoorTypeId_t typeId)
{
    bool b_doorSwitchErrorState = false;

    if(typeId < DOOR_MAX_NUMBER)
    {
        if(true == ary_DoorState[typeId].f_DoorOpenAlarm)
        {
            b_doorSwitchErrorState = true;
        }
    }

    return b_doorSwitchErrorState;
}

bool Get_DoorSwitchErrorState(DoorTypeId_t typeId)
{
    bool b_doorSwitchErrorState = false;

    if(typeId < DOOR_MAX_NUMBER)
    {
        if(true == ary_DoorState[typeId].f_DoorSwitchError)
        {
            b_doorSwitchErrorState = true;
        }
    }

    return b_doorSwitchErrorState;
}

uint8_t Get_DoorSwitchflagState(DoorTypeId_t typeId)
{
    uint8_t u8_doorSwitchFlagState = 0;

    if(typeId < (DoorTypeId_t)DOOR_MAX_NUMBER)
    {
        BIT_WRITE(u8_doorSwitchFlagState, 0, ary_DoorState[typeId].f_DoorSwitchState);
        BIT_WRITE(u8_doorSwitchFlagState, 1, ary_DoorState[typeId].f_DoorState);
        BIT_WRITE(u8_doorSwitchFlagState, 2, ary_DoorState[typeId].f_DoorOpenClose);
        BIT_WRITE(u8_doorSwitchFlagState, 3, ary_DoorState[typeId].f_DoorOpenAlarm);
        BIT_WRITE(u8_doorSwitchFlagState, 4, ary_DoorState[typeId].f_DoorDefaultClose);
        BIT_WRITE(u8_doorSwitchFlagState, 5, ary_DoorState[typeId].f_DoorSwitchError);
    }

    return u8_doorSwitchFlagState;
}

