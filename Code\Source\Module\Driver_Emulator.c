/*!
 * @file
 * @brief This module covers the complete fan functionality.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "Driver_DoorSwitch.h"
#include "SystemTimerModule.h"
#include "Driver_Flash.h"
#include "Driver_Emulator.h"
#include "CloudControl.h"

#if CONIG_EMULATOR_ENABLE

fridge_emmulator_st emmulator = {
    .sensor_ref = CON_20P0_DEGREE,
    .sensor_ref_error = false,
    .sensor_ref_defrost = CON_20P0_DEGREE,
    .sensor_ref_defrost_error = false,
    .sensor_vv = CON_20P0_DEGREE,
    .sensor_vv_error = false,
    .sensor_ac = 0,
    .sensor_ac_error = false,
    .sensor_dc = 0,
    .sensor_dc_error = false,
    .sensor_humidity = HUMIDITY_AD_PCT25,
    .sensor_humidity_error = false,
    .sensor_room = CON_20P0_DEGREE,
    .sensor_room_error = false,
    .sensor_frz = CON_20P0_DEGREE,
    .sensor_frz_error = false,
    .sensor_defrost = CON_20P0_DEGREE,
    .sensor_defrost_error = false,
    .sensor_icemaker_bottom = CON_20P0_DEGREE,
    .sensor_icemaker_bottom_error = false,
    .sensor_icemaker_top = CON_20P0_DEGREE,
    .sensor_icemaker_top_error = false,
    .inverter_comm_err = false,
    .display_comm_err = false,
    .icemaker_comm_err = false,
    .nfc_comm_err = false,
    .rt = RT_BELOW35,
    .rh = HBELOW25,
    .fast_forward = 0,
};

RoomTempRange_t Get_RoomTempRange(void)
{
    return emmulator.rt;
}

HumidityRange_t Get_HumidityRange(void)
{
    return emmulator.rh;
}

uint16_t Get_SensorValue(SensorType_t type)
{
    switch(type)
    {
        case SENSOR_REF:
            return emmulator.sensor_ref;
        case SENSOR_REF_DEFROST:
            return emmulator.sensor_ref_defrost;
        case SENSOR_VV:
            return emmulator.sensor_vv;
        case SENSOR_AC:
            return emmulator.sensor_ac;
        case SENSOR_DC:
            return emmulator.sensor_dc;
        case SENSOR_HUMIDITY:
            return emmulator.sensor_humidity;
        case SENSOR_ROOM:
            return emmulator.sensor_room;
        case SENSOR_FRZ:
            return emmulator.sensor_frz;
        case SENSOR_DEFROST:
            return emmulator.sensor_defrost;
        case SENSOR_ICEMAKER_BOTTOM:
            return emmulator.sensor_icemaker_bottom;
        case SENSOR_ICEMAKER_TOP:
            return emmulator.sensor_icemaker_top;
        case SENSOR_ICEMAKER_BOTTOMX:
            return emmulator.sensor_icemaker_bottomx;
        default:
            return 0;
    }
}

bool Get_SensorError(SensorType_t type)
{
    switch(type)
    {
        case SENSOR_REF:
            return emmulator.sensor_ref_error;
        case SENSOR_REF_DEFROST:
            return emmulator.sensor_ref_defrost_error;
        case SENSOR_VV:
            return emmulator.sensor_vv_error;
        case SENSOR_AC:
            return emmulator.sensor_ac_error;
        case SENSOR_DC:
            return emmulator.sensor_dc_error;
        case SENSOR_HUMIDITY:
            return emmulator.sensor_humidity_error;
        case SENSOR_ROOM:
            return emmulator.sensor_room_error;
        case SENSOR_FRZ:
            return emmulator.sensor_frz_error;
        case SENSOR_DEFROST:
            return emmulator.sensor_defrost_error;
        case SENSOR_ICEMAKER_BOTTOM:
            return emmulator.sensor_icemaker_bottom_error;
        case SENSOR_ICEMAKER_TOP:
            return emmulator.sensor_icemaker_top_error;
        case SENSOR_ICEMAKER_BOTTOMX:
            return emmulator.sensor_icemaker_bottomx_error;
        default:
            return false;
    }
}
bool Get_InverterCommErr(void)
{
    return emmulator.inverter_comm_err;
}

bool Get_DisplayCommErr(void)
{
    return emmulator.display_comm_err;
}
bool Get_IceMakerCommErr(void)
{
    return emmulator.icemaker_comm_err;
}

bool Get_NfcCommErr(void)
{
    return emmulator.nfc_comm_err;
}
uint8_t emmulator_ref_left_door_in()
{
    return emmulator.ref_left_door_in;
}

uint8_t emmulator_ref_right_door_in()
{
    return emmulator.ref_right_door_in;
}

uint8_t emmulator_frz_left_door_in()
{
    return emmulator.frz_left_door_in;
}

uint8_t emmulator_frz_right_door_in()
{
    return emmulator.frz_right_door_in;
}

uint16_t Get_DoorOpenTimeSecond(DoorTypeId_t typeId)
{
    switch(typeId)
    {
        case DOOR_REF_LEFT:
            return emmulator.ref_left_door_open_time;
        case DOOR_REF_RIGHT:
            return emmulator.ref_right_door_open_time;
        case DOOR_FRZ_LEFT:
            return emmulator.frz_left_door_open_time;
        case DOOR_FRZ_RIGHT:
            return emmulator.frz_right_door_open_time;
        case DOOR_REF:
            return emmulator.ref_door_open_time;
        case DOOR_FRZ:
            return emmulator.frz_door_open_time;
        case DOOR_ALL:
            return emmulator.all_door_open_time;
        default:
            return 0;
    }
}

uint8_t Get_DoorOpenCloseCounter(DoorTypeId_t typeId)
{
    switch(typeId)
    {
        case DOOR_REF_LEFT:
            return emmulator.ref_left_door_open_count;
        case DOOR_REF_RIGHT:
            return emmulator.ref_right_door_open_count;
        case DOOR_FRZ_LEFT:
            return emmulator.frz_left_door_open_count;
        case DOOR_FRZ_RIGHT:
            return emmulator.frz_right_door_open_count;
        case DOOR_REF:
            return emmulator.ref_door_open_count;
        case DOOR_FRZ:
            return emmulator.frz_door_open_count;
        case DOOR_ALL:
            return emmulator.all_door_open_count;
        default:
            return 0;
    }
}

void Clear_DoorOpenTimeSecond(DoorTypeId_t typeId)
{
    switch(typeId)
    {
        case DOOR_REF_LEFT:
            emmulator.ref_left_door_open_time = 0;
            break;
        case DOOR_REF_RIGHT:
            emmulator.ref_right_door_open_time = 0;
            break;
        case DOOR_FRZ_LEFT:
            emmulator.frz_left_door_open_time = 0;
            break;
        case DOOR_FRZ_RIGHT:
            emmulator.frz_right_door_open_time = 0;
            break;
        case DOOR_REF:
            emmulator.ref_left_door_open_time = 0;
            emmulator.ref_right_door_open_time = 0;
            emmulator.ref_door_open_time = 0;
            break;
        case DOOR_FRZ:
            emmulator.frz_left_door_open_time = 0;
            emmulator.frz_right_door_open_time = 0;
            emmulator.frz_door_open_time = 0;
            break;
        case DOOR_ALL:
            emmulator.ref_left_door_open_time = 0;
            emmulator.ref_right_door_open_time = 0;
            emmulator.ref_door_open_time = 0;
            emmulator.frz_left_door_open_time = 0;
            emmulator.frz_right_door_open_time = 0;
            emmulator.frz_door_open_time = 0;
            emmulator.all_door_open_time = 0;
            break;
        default:
            return;
    }
}

#endif

void Driver_Emulator_Run(void)
{
#if CONIG_EMULATOR_ENABLE
    if(emmulator.fast_forward)
    {
        Shorten_Timer(emmulator.fast_forward);
        emmulator.fast_forward = 0;
    }

    if(emmulator.refion_enable == 1)
    {
        Set_RefIonEnable(1);
        emmulator.refion_enable = 0;
    }
    else if(emmulator.refion_enable == 2)
    {
        Set_RefIonEnable(0);
        emmulator.refion_enable = 0;
    }

    if(emmulator.frzion_enable == 1)
    {
        Set_FrzIonEnable(1);
        emmulator.frzion_enable = 0;
    }
    else if(emmulator.frzion_enable == 2)
    {
        Set_FrzIonEnable(0);
        emmulator.frzion_enable = 0;
    }

    if(emmulator.linyun_mute_enable == 1)
    {
        SetSysParam(SYSPARAM_LINGYUN_MUTE, 1);
        emmulator.linyun_mute_enable = 0;
    }
    else if(emmulator.frzion_enable == 2)
    {
        SetSysParam(SYSPARAM_LINGYUN_MUTE, 0);
        Set_Mute_Mode(LINYUN_MUTE_LOCAL);
        emmulator.linyun_mute_enable = 0;
    }

    if(emmulator.linyun_mute_mode > 0)
    {
        uint8_t enable;
        GetSysParam(SYSPARAM_LINGYUN_MUTE, &enable);
        if(enable > 0)
        {
            Set_Mute_Mode(emmulator.linyun_mute_mode - 1);
        }
        emmulator.linyun_mute_mode = 0;
    }
#endif
}
