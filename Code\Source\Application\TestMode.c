/*!
 * @file
 * @brief Manages all the state variables of the test mode.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "TestMode.h"
#include "Core_CallBackTimer.h"
#include "ResolverDevice.h"
#include "Driver_GradualLamp.h"
#include "Driver_SingleDamper.h"
#include "IO_Device.h"
#include "Driver_CompFrequency.h"
#include "SystemManager.h"
#include "Driver_Fan.h"
#include "DisplayInterface.h"
#include "Driver_AdSample.h"
#include "FridgeRunner.h"
#include "SystemTimerModule.h"
#include "Sbus_Display.h"
#include "Drive_Valve.h"

#define U16_TEST_MODE_CYCLE_SECOND (uint16_t)1

static st_CoreCallbackTimer st_TestModeTimer;
static uint16_t u16_TestControlCount;
static bool b_DamperInit;
static bool b_DefrostInit;
static uint8_t u8_TestModeSave;

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_TestModeTimer);
}

static void TestMode_ControlCooling(void)
{
    if(false == b_DamperInit)
    {
        b_DamperInit = true;
        Reset_SingleDamper(Damper_ID0);
        Drive_ValveReset();
        u16_TestControlCount = Get_MinuteCount();
    }

    if(b_DefrostInit == true)
    {
        Defrosting_Exit();
        Clear_DefrostMode();
        b_DefrostInit = false;
    }

    if(Get_MinuteElapsedTime(u16_TestControlCount) < 1)
    {
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_CoolFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamperHeater, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzIonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_AllOpen);
    }
    else
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, FREQ_144HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 100);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 100);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DAMPER_AllOpen);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DAMPER_AllOpen);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 100);
    }
}

static void TestMode_ControlFrzCooling(void)
{
    if(false == b_DamperInit)
    {
        b_DamperInit = true;
        Reset_SingleDamper(Damper_ID0);
        Drive_ValveReset();
        u16_TestControlCount = Get_MinuteCount();
    }

    if(b_DefrostInit == true)
    {
        Defrosting_Exit();
        Clear_DefrostMode();
        b_DefrostInit = false;
    }

    if(Get_MinuteElapsedTime(u16_TestControlCount) < 1)
    {
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_CoolFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamperHeater, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzIonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_FrzON_RefOFF);
    }
    else
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, FREQ_144HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 100);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 100);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DAMPER_AllOpen);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DAMPER_AllOpen);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 100);
    }
}

static void TestMode_ControlRefCooling(void)
{
    if(false == b_DamperInit)
    {
        b_DamperInit = true;
        Reset_SingleDamper(Damper_ID0);
        Drive_ValveReset();
        u16_TestControlCount = Get_MinuteCount();
    }

    if(b_DefrostInit == true)
    {
        Defrosting_Exit();
        Clear_DefrostMode();
        b_DefrostInit = false;
    }

    if(Get_MinuteElapsedTime(u16_TestControlCount) < 1)
    {
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_CoolFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamperHeater, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzIonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_FrzOFF_RefON);
    }
    else
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, FREQ_144HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 100);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 100);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DAMPER_AllOpen);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DAMPER_AllOpen);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 100);
    }
}


static void TestMode_ControlDefrosting(void)
{
    DefrostMode_t defrost_mode = Get_DefrostMode();

    if(false == b_DamperInit)
    {
        b_DamperInit = true;
        Reset_SingleDamper(Damper_ID0);
        Drive_ValveReset();
        u16_TestControlCount = Get_MinuteCount();
    }

    if(false == b_DefrostInit)
    {
        b_DefrostInit = true;
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_CoolFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamperHeater, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzIonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DS_DontCare);
        Set_DeforstExitTemp(CON_15P0_DEGREE);
        Defrosting_Init(eEnterState_Manual);
    }

    if((DefrostMode_t)eDefrostMode_AfterComp < defrost_mode)
    {
        Defrosting_Exit();
        Clear_DefrostMode();
        FridgeState_Update(eFridge_Startup);
    }
}

static void Process_TestMode(void)
{
    uint8_t u8_TestMode = Get_TestMode();

    if(u8_TestModeSave != u8_TestMode)
    {
        u8_TestModeSave = u8_TestMode;
        u16_TestControlCount = Get_MinuteCount();
        
    }
    switch(u8_TestMode)
    {
        case TMODE_FORCE_COOLING:
            TestMode_ControlCooling();
            break;
        case TMODE_FORCE_FRZCOOLING:
            TestMode_ControlFrzCooling();
            break;
        case TMODE_FORCE_REFCOOLING:
            TestMode_ControlRefCooling();
            break;
        case TMODE_FORCE_DEFROST:
            TestMode_ControlDefrosting();
            break;
        case TMODE_FORCE_ENERGY:
            Force_EnterEnergyConsumptionModeState();
            FridgeState_Update(eFridge_Startup);
            break;
        case TMODE_TT:
            FridgeState_Update(eFridge_Startup);
            break;
        default:
            break;
    }
}

static void Start_PollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_TestModeTimer,
        Process_TestMode,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

void TestMode_Init(void)
{
    b_DamperInit = false;
    b_DefrostInit = false;
    u8_TestModeSave = 0;
    Start_PollTimer(U16_TEST_MODE_CYCLE_SECOND);
}

void TestMode_Exit(void)
{
    if(b_DefrostInit == true)
    {
        Defrosting_Exit();
        Clear_DefrostMode();
        b_DefrostInit = false;
    }
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefFan, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzFan, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_CoolFan, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDamper, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamper, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzDefHeater, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamperHeater, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzIonGenerator, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDefHeater, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DS_DontCare);
    Stop_PollTimer();
    Set_CoolingEntryMode(eMode_FridgePowerOn);
}
