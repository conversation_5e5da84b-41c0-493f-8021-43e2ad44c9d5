/*!
 * @file
 * @brief This file defines public constants, types and functions for the fan drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __DRIVER_EMULATOR_H
#define __DRIVER_EMULATOR_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "Driver_AdSample.h"

#define CONIG_EMULATOR_ENABLE 0

#if CONIG_EMULATOR_ENABLE
uint8_t emmulator_ref_left_door_in();
uint8_t emmulator_ref_right_door_in();
uint8_t emmulator_frz_left_door_in();
uint8_t emmulator_frz_right_door_in();

#define __EMULATOR__FUNCITON __WEAK
#undef IO_REF_LEFT_DOOR_IN
#undef IO_REF_RIGHT_DOOR_IN
#undef IO_FRZ_LEFT_DOOR_IN
#undef IO_FRZ_RIGHT_DOOR_IN
#define IO_REF_LEFT_DOOR_IN emmulator_ref_left_door_in()
#define IO_REF_RIGHT_DOOR_IN emmulator_ref_right_door_in()
#define IO_FRZ_LEFT_DOOR_IN emmulator_frz_left_door_in()
#define IO_FRZ_RIGHT_DOOR_IN emmulator_frz_right_door_in()
#else
#define __EMULATOR__FUNCITON
#endif

typedef struct
{
    uint16_t sensor_ref;
    bool sensor_ref_error;
    uint16_t sensor_ref_defrost;
    bool sensor_ref_defrost_error;
    uint16_t sensor_vv;
    bool sensor_vv_error;
    uint16_t sensor_ac;
    bool sensor_ac_error;
    uint16_t sensor_dc;
    bool sensor_dc_error;
    uint16_t sensor_humidity;
    bool sensor_humidity_error;
    uint16_t sensor_room;
    bool sensor_room_error;
    uint16_t sensor_frz;
    bool sensor_frz_error;
    uint16_t sensor_defrost;
    bool sensor_defrost_error;
    uint16_t sensor_icemaker_bottom;
    bool sensor_icemaker_bottom_error;
    uint16_t sensor_icemaker_top;
    bool sensor_icemaker_top_error;
    uint16_t sensor_icemaker_bottomx;
    bool sensor_icemaker_bottomx_error;
    bool inverter_comm_err;
    bool display_comm_err;
    bool icemaker_comm_err;
    bool nfc_comm_err;
    RoomTempRange_t rt;
    HumidityRange_t rh;
    uint8_t ref_left_door_in;
    uint8_t ref_right_door_in;
    uint8_t frz_left_door_in;
    uint8_t frz_right_door_in;
    uint16_t ref_left_door_open_time;
    uint16_t ref_right_door_open_time;
    uint16_t ref_door_open_time;
    uint16_t frz_left_door_open_time;
    uint16_t frz_right_door_open_time;
    uint16_t frz_door_open_time;
    uint16_t all_door_open_time;
    uint8_t ref_left_door_open_count;
    uint8_t ref_right_door_open_count;
    uint8_t ref_door_open_count;
    uint8_t frz_left_door_open_count;
    uint8_t frz_right_door_open_count;
    uint8_t frz_door_open_count;
    uint8_t all_door_open_count;
    uint16_t fast_forward;
    uint8_t refion_enable;
    uint8_t frzion_enable;
    uint8_t linyun_mute_enable;
    uint8_t linyun_mute_mode;
} fridge_emmulator_st;

void Driver_Emulator_Run(void);
#endif
