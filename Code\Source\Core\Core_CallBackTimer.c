/*!
 * @file
 * @brief Enables timers to be used with callback functions and provides a way for
 * timers to be used without having to poll them.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Core_TimeBase.h"
#include "Core_CallBackTimer.h"
#include "CORE_Assert.h"

void Core_CallbackTimer_TimerStart(
    st_CoreCallbackTimer *pst_CallBackTimer,
    fpTimerCallBackFunction fp_CallBackFunction,
#if(true == CALLBACK_TIMER_CALLBACK_DATA_ENABLED)
    void *p_CallBackData,
#endif
    uint16_t u16_DurationSeconds,
    uint16_t u16_DurationMilliSeconds,
    EN_Core_CallbackTimer_Type en_TimerType,
    EN_Core_CallbackTimer_Priority en_Priority);

void Core_CallbackTimer_TimerStop(
    st_CoreCallbackTimer *pst_CallBackTimer);

void Core_CallbackTimer_TimeRemaining(
    st_CoreCallbackTimer *pst_CallBackTimer, uint16_t *pu16_Seconds, uint16_t *pu16_Milliseconds);

void Core_CallbackTimer_TimerStart(
    st_CoreCallbackTimer *pst_CallBackTimer,
    fpTimerCallBackFunction fp_CallBackFunction,
#if(true == CALLBACK_TIMER_CALLBACK_DATA_ENABLED)
    void *p_CallBackData,
#endif
    uint16_t u16_DurationSeconds,
    uint16_t u16_DurationMilliSeconds,
    EN_Core_CallbackTimer_Type en_TimerType,
    EN_Core_CallbackTimer_Priority en_Priority)
{
    ENSURE(en_TimerType < eCallbackTimer_Type_Max);
    ENSURE(en_Priority < eCallbackTimer_Priority_Max);
    ENSURE(pst_CallBackTimer != (st_CoreCallbackTimer *)(NULL));

    pst_CallBackTimer->u16_DurationSeconds = u16_DurationSeconds;
    pst_CallBackTimer->u16_DurationMilliSeconds = u16_DurationMilliSeconds;

    pst_CallBackTimer->u8_TimerType = (uint8_t)en_TimerType;

    pst_CallBackTimer->u8_Priority = (uint8_t)en_Priority;

    pst_CallBackTimer->fp_CallBackFunction = fp_CallBackFunction;

    if(eCallbackTimer_Type_Periodic == en_TimerType)
    {
        TinyTimerModule_StartPeriodic(
            &(pst_CallBackTimer->st_Timer),
            (TinyTimerTicks_t)(u16_DurationSeconds * CORE_TIMEBASE_NUM_MILLISECONDS_PER_SECOND + u16_DurationMilliSeconds),
            (TinyTimerCallback_t)fp_CallBackFunction,
            NULL);
    }
    else
    {
        TinyTimerModule_StartOneShot(
            &(pst_CallBackTimer->st_Timer),
            (TinyTimerTicks_t)(u16_DurationSeconds * CORE_TIMEBASE_NUM_MILLISECONDS_PER_SECOND + u16_DurationMilliSeconds),
            (TinyTimerCallback_t)fp_CallBackFunction,
            NULL);
    }
}

void Core_CallbackTimer_TimerStop(
    st_CoreCallbackTimer *pst_CallBackTimer)
{
    if(pst_CallBackTimer->u8_Priority < (uint8_t)eCallbackTimer_Priority_Max)
    {
        TinyTimerModule_Stop(&(pst_CallBackTimer->st_Timer));
    }
}

void Core_CallbackTimer_TimeRemaining(
    st_CoreCallbackTimer *pst_CallBackTimer, uint16_t *pu16_Seconds, uint16_t *pu16_Milliseconds)
{
    uint16_t u16_RemainingTicks;

    if(((uint16_t *)NULL != pu16_Seconds) &&
        ((uint16_t *)NULL != pu16_Milliseconds))
    {
        u16_RemainingTicks = TinyTimerModule_RemainingTicks(&(pst_CallBackTimer->st_Timer));
        if(u16_RemainingTicks > 0)
        {
            *pu16_Seconds = u16_RemainingTicks / CORE_TIMEBASE_NUM_MILLISECONDS_PER_SECOND;
            *pu16_Milliseconds = u16_RemainingTicks % CORE_TIMEBASE_NUM_MILLISECONDS_PER_SECOND /
                COREUSER_TIMEBASE_NUM_TICKS_PER_MILLISECOND;
        }
        else
        {
            *pu16_Seconds = 0;
            *pu16_Milliseconds = 0;
        }
    }
}
