/**
 ******************************************************************************
 * @file   ddl.h
 *
 * @brief This file contains all the functions prototypes of the DDL driver
 *        library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-15       MADS            First version
 @endverbatim
 ******************************************************************************
 * Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************
 */

#ifndef __DDL_H__
#define __DDL_H__

/******************************************************************************/
/* Include files                                                              */
/******************************************************************************/
#include "base_types.h"
#include "board_stkhc32l186.h"
#include "HC32L186.h"
#include "system_hc32l186.h"
#include "sysctrl.h"
#include "interrupts_hc32l186.h"
/* C binding of definitions if building with C++ compiler                     */
#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @addtogroup HC32L186_DDL 驱动库
     * @{
     */

    /**
     * @addtogroup DDL_DDL DDL模块驱动库
     * @{
     */

    /******************************************************************************/
    /* Global pre-processor symbols/macros ('#define')                            */
    /******************************************************************************/
    /**
     * @defgroup DDL_Global_Macros DDL全局宏定义
     * @{
     */

#define DDL_ZERO_STRUCT(x) ddl_memclr((uint8_t *)&(x), (uint32_t)(sizeof(x))) /*!< 结构体清零 */

#define DEC2BCD(x) ((((x) / 10) << 4) + ((x) % 10)) /*!< 十进制转BCD */
#define BCD2DEC(x) ((((x) >> 4) * 10) + ((x) & 0x0F)) /*!< BCD转十进制 */

#define SET_BIT(REG, BIT) ((REG) |= (BIT)) /*!< 寄存器位置位 */

#define CLEAR_BIT(REG, BIT) ((REG) &= ~(BIT)) /*!< 寄存器位清零 */

#define READ_BIT(REG, BIT) ((REG) & (BIT)) /*!< 寄存器位读取 */

#define CLEAR_REG(REG) ((REG) = (0x0)) /*!< 寄存器清零 */

#define WRITE_REG(REG, VAL) ((REG) = (VAL)) /*!< 寄存器写入 */

#define READ_REG(REG) ((REG)) /*!< 寄存器读取 */

#define MODIFY_REG(REG, CLEARMASK, SETMASK) WRITE_REG((REG), (((READ_REG(REG)) & (~(CLEARMASK))) | (SETMASK))) /*!< 寄存器修改 */

/**
 * @brief Global Device Series List
 */
#define DDL_DEVICE_SERIES_HC32L186 (0u)

/**
 * @brief package definitions of HC device.
 */
// #define DDL_DEVICE_PACKAGE_HC_C         (0x00u)
// #define DDL_DEVICE_PACKAGE_HC_F         (0x10u)
// #define DDL_DEVICE_PACKAGE_HC_J         (0x20u)
// #define DDL_DEVICE_PACKAGE_HC_K         (0x30u)

/**
 * @}
 */

/******************************************************************************/
/* User Device Setting Include file                                           */
/******************************************************************************/
#include "ddl_device.h" // MUST be included here!

    /******************************************************************************/
    /* Global type definitions ('typedef')                                        */
    /******************************************************************************/
    /**
     * @defgroup DDL_Global_Types DDL全局类型定义
     * @{
     */
    /**
     * @brief Level
     * @note Specifies levels
     */
    typedef enum
    {
        DdlLow = 0u, /*!< Low level  '0' */
        DdlHigh = 1u /*!< High level '1' */
    } en_level_t;

    /**
     * @brief Generic Flag Code
     * @note Specifies flags
     */
    typedef enum
    {
        DdlClr = 0u, /*!< Flag clr '0' */
        DdlSet = 1u /*!< Flag set '1' */
    } en_stat_flag_t,
        en_irq_flag_t;
    /**
     * @}
     */
    /******************************************************************************/
    /* Global variable declarations ('extern', definition in C source)            */
    /******************************************************************************/

    /******************************************************************************/
    /* Global function prototypes ('extern', definition in C source)              */
    /******************************************************************************/

    /*******************************************************************************
     * Global function prototypes
     ******************************************************************************/
    /**
     * @addtogroup DDL_Global_Functions DDL全局函数定义
     * @{
     */
    void ddl_memclr(void *pu8Address, uint32_t u32Count);
    uint32_t Log2(uint32_t u32Val);

    void delay1ms(uint32_t u32Cnt);
    void delay100us(uint32_t u32Cnt);
    void delay10us(uint32_t u32Cnt);

    void SetBit(uint32_t addr, uint32_t offset, boolean_t bFlag);
    void ClrBit(uint32_t addr, uint32_t offset);
    boolean_t GetBit(uint32_t addr, uint32_t offset);

    /**
     * @}
     */

    /**
     * @}
     */

    /**
     * @}
     */

#ifdef __cplusplus
}
#endif

#endif /* __DDL_H__ */

/******************************************************************************/
/* EOF (not truncated)                                                        */
/******************************************************************************/
