/*!
 * @file
 * @brief Statically linked list. Nodes are allocated by clients.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved.
 */

#ifndef TINYLINKEDLIST_H
#define TINYLINKEDLIST_H

#include <stdbool.h>

typedef struct TinyLinkedListNode_t
{
    struct
    {
        struct TinyLinkedListNode_t *next;
    } _private;
} TinyLinkedListNode_t;

typedef struct
{
    struct
    {
        TinyLinkedListNode_t head;
    } _private;
} TinyLinkedList_t;

typedef struct
{
    struct
    {
        TinyLinkedListNode_t *current;
    } _private;
} TinyLinkedListIterator_t;

/*!
 * @param instance
 */
void TinyLinkedList_Init(TinyLinkedList_t *instance);

/*!
 * @param instance
 * @param node
 */
void TinyLinkedList_Insert(TinyLinkedList_t *instance,
    TinyLinkedListNode_t *node);

/*!
 * @param instance
 * @param node
 */
void TinyLinkedList_Remove(TinyLinkedList_t *instance,
    TinyLinkedListNode_t *node);

/*!
 * Initialize an iterator for the provided list.
 * @param instance
 * @param list
 */
void TinyLinkedListIterator_Init(TinyLinkedListIterator_t *instance,
    TinyLinkedList_t *list);

/*!
 * Return a pointer to the next node or NULL if there are no more nodes.
 * @param instance
 * @param list
 * @return
 */
TinyLinkedListNode_t *
    TinyLinkedListIterator_Next(TinyLinkedListIterator_t *instance,
        TinyLinkedList_t *list);

#define TinyLinkedList_ForEach(_list, _type, _item, ...)                   \
    do                                                                     \
    {                                                                      \
        TinyLinkedListIterator_t _it;                                      \
        TinyLinkedListIterator_Init(&_it, _list);                          \
        _type *_item;                                                      \
        while((_item = (_type *)TinyLinkedListIterator_Next(&_it, _list))) \
        {                                                                  \
            __VA_ARGS__                                                    \
        }                                                                  \
    } while(0)

#endif
