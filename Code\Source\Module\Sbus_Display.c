/*!
 * @file
 * @brief Manages all the state variables of the cooling controller.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "Crc16_CCITT_FALSE.h"
#include "Parameter_TemperatureZone.h"
#include "SystemTimerModule.h"
#include "DisplayInterface.h"
#include "Driver_DoorSwitch.h"
#include "SystemManager.h"
#include "Defrosting.h"
#include "FridgeRunner.h"
#include "CoolingCycle.h"
#include "Driver_CompFrequency.h"
#include "Driver_Fan.h"
#include "Driver_SingleDamper.h"
#include "VerticalBeamHeater.h"
#include "CoolingCycle.h"
#include "ResolverDevice.h"
#include "Driver_Flash.h"
#include "Drive_Valve.h"
#include "Sbus_IceMaker.h"
#include "Sbus_Display.h"
#include "Sbus_Nfc.h"
#include "InverterUsart.h"
#include "ParameterManager.h"
#include "FactoryMode.h"
#include "FaultCode.h"
#include "IO_Device.h"
#include "Iot_Spec.h"
#include "miio_api.h"
#include "syslog.h"
#include "Driver_Emulator.h"

display_param_st display_param = {
    .mode = eManual_Mode,
    .ref_set_temp = REF_LEVEL_5 - 1,
    .frz_set_temp = FRZ_LEVEL_F18 - 30,
    .infant_mode = eRefVar_FreshMeat,
    .icemaker_mode = eIceMaker_Stop_Mode,
    .force_state = 0,
    .wifi_match = 1,
    .mtype = MACHINE_TYPE_UNKOWN,
    .init = 1,
};

sbus_display_st display;

static void sync_mode(void *data)
{
    uint8_t mode = *(uint8_t *)data;

    if(display_param.init == 1)
    {
        return;
    }
    Set_UserMode(mode);
}

static bool update_mode(void)
{
    uint8_t mode = Get_UserMode();

    if(display_param.mode != mode)
    {
        display_param.mode = mode;
        return true;
    }
    return false;
}

static void sync_ref_set(void *data)
{
    uint8_t temp = *(uint8_t *)data;

    if(display_param.init == 1)
    {
        return;
    }
    Update_RefSetTemp(temp + 1);
}

static void sync_ref_disable(void *data)
{
    uint8_t temp = *(uint8_t *)data;

    if(display_param.init == 1)
    {
        return;
    }
    Set_RefDisable(temp);
}

static bool update_ref_disable(void)
{
    uint8_t data;

    GetSysParam(SYSPARAM_REF_DISABLE, &data);
    if(display_param.ref_disable != data)
    {
        display_param.ref_disable = data;
        return true;
    }
    return false;
}

static bool update_ref_temp(void)
{
    int8_t temp;

    temp = (Get_SensorValue(SENSOR_REF) - 500) / 10;
    if(display_param.ref_temp != temp)
    {
        display_param.ref_temp = temp;
        return true;
    }
    return false;
}

static bool update_ref_set(void)
{
    int8_t refSet;
    uint8_t val;

    GetSysParam(SYSPARAM_REFTEMP, &val);
    refSet = val - 1;

    if(display_param.ref_set_temp != refSet)
    {
        display_param.ref_set_temp = refSet;
        return true;
    }
    return false;
}

static void sync_frz_set(void *data)
{
    int8_t temp = *(uint8_t *)data;

    if(display_param.init == 1)
    {
        return;
    }
    Update_FrzSetTemp(temp + 30);
}

static bool update_frz_temp(void)
{
    int8_t temp;

    temp = (Get_SensorValue(SENSOR_FRZ) - 500) / 10;
    if(display_param.frz_temp != temp)
    {
        display_param.frz_temp = temp;
        return true;
    }
    return false;
}

static bool update_frz_set(void)
{
    int8_t frzSet;
    uint8_t val;

    GetSysParam(SYSPARAM_FRZTEMP, &val);
    frzSet = val - 30;

    if(display_param.frz_set_temp != frzSet)
    {
        display_param.frz_set_temp = frzSet;
        return true;
    }
    return false;
}

static void sync_infant_mode(void *data)
{
    uint8_t mode = *(uint8_t *)data;

    if(display_param.init == 1)
    {
        return;
    }
    Update_RefVarSetTemp(mode);
}

static bool update_infant_mode(void)
{
    uint8_t refVarSet;

    refVarSet = Get_RefVarSetTemp();
    if(display_param.infant_mode != refVarSet)
    {
        display_param.infant_mode = refVarSet;
        return true;
    }
    return false;
}

static bool update_var_temp(void)
{
    int8_t temp;

    temp = (Get_SensorValue(SENSOR_VV) - 500) / 10;
    if(display_param.var_temp != temp)
    {
        display_param.var_temp = temp;
        return true;
    }
    return false;
}

static bool update_vbottom_temp(void)
{
    int8_t temp;

    temp = (Get_SensorValue(SENSOR_ICEMAKER_BOTTOM) - 500) / 10;
    if(display_param.vbottom_temp != temp)
    {
        display_param.vbottom_temp = temp;
        return true;
    }
    return false;
}

static bool update_vtop_temp(void)
{
    int8_t temp;

    temp = (Get_SensorValue(SENSOR_ICEMAKER_TOP) - 500) / 10;
    if(display_param.vtop_temp != temp)
    {
        display_param.vtop_temp = temp;
        return true;
    }
    return false;
}

static bool update_vbottomx_temp(void)
{
    int8_t temp;

    temp = (Get_SensorValue(SENSOR_ICEMAKER_BOTTOMX) - 500) / 10;
    if(display_param.vbottomx_temp != temp)
    {
        display_param.vbottomx_temp = temp;
        return true;
    }
    return false;
}

static void sync_poweron(property_reponse_e result)
{
    if(PROPERTY_RESPONSE_SUCCESS == result)
    {
        display.init = true;
        display_param.init = 0;
    }
}

static void sync_icemaker(void *data)
{
    uint8_t mode = *(uint8_t *)data;
    if(display_param.init == 1)
    {
        return;
    }
    SetSysParam(SYSPARAM_ICEMAKER_FUNC, mode);
}

static void sync_icemaker_reserve(void *data)
{
    if(display_param.init == 1)
    {
        return;
    }

    SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_RESERVE, data);
}

static void sync_icemaker_volume(void *data)
{
    uint8_t vol = *(uint8_t *)data;
    if(display_param.init == 1)
    {
        return;
    }
    SetSysParam(SYSPARAM_ICEMAKER_VOLUME, vol);
}

static void sync_icemaker_reserve_sw(void *data)
{
    if(display_param.init == 1)
    {
        return;
    }

    SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_RESERVE_STATE, data);
}

static bool update_icemaker_reserve(void)
{
    uint32_t timer;

    GetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_RESERVE, &timer);

    if(display_param.icemaker_reserve != timer)
    {
        display_param.icemaker_reserve = timer;
        return true;
    }
    return false;
}

static bool update_icemaker_reserve_sw(void)
{
    uint8_t reserve;

    GetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_RESERVE_STATE, &reserve);

    if(display_param.icemaker_reserve_sw != reserve)
    {
        display_param.icemaker_reserve_sw = reserve;
        return true;
    }
    return false;
}

static bool update_icemaker_mode(void)
{
    uint8_t ice_maker;

    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &ice_maker);
    if(display_param.icemaker_mode != ice_maker)
    {
        display_param.icemaker_mode = ice_maker;
        return true;
    }
    return false;
}

static bool update_icemaker_volume(void)
{
    uint8_t vol;

    GetSysParam(SYSPARAM_ICEMAKER_VOLUME, &vol);
    if(display_param.icemaker_volume != vol)
    {
        display_param.icemaker_volume = vol;
        return true;
    }
    return false;
}

static bool update_icemaker_state(void)
{
    uint8_t icemaker_state = 0;
    int8_t ret;

    ret = GetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_STATE, &icemaker_state);
    if(ret == FIREWARE_SUCCESS &&
        display_param.icemaker_state != icemaker_state)
    {
        display_param.icemaker_state = icemaker_state;
        return true;
    }
    return false;
}

static void sync_icemaker_test(void *data)
{
    SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_TEST, data);
}

static void sync_force_state(void *data)
{
    uint8_t force = *(uint8_t *)data;
    uint8_t val;
    if(force & FORCE_SKIP_INSPECTION)
    {
        SetSysParam(SYSPARAM_INSPECTION, 1);
        Set_FactoryEntryNumber(1);
        if(Get_FridgeState() == eFridge_Factory)
        {
            display.b_reboot_wifi = true;
            FridgeState_Update((FridgeState_t)eFridge_Startup);
        }
        force &= ~FORCE_SKIP_INSPECTION;
    }

    if(force & FORCE_VALVE_OPEN)
    {
        if(IsValveAlreadyReset() == false)
        {
            Drive_ValveReset();
        }
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_AllOpen);
        FridgeState_Update((FridgeState_t)eFridge_Showroom);
        force &= ~FORCE_VALVE_OPEN;
    }

    if(force & FORCE_STATE_VERTICAL_HEATER)
    {
        Forced_VerticalBeamHeaterState(true);
    }
    else if((force & FORCE_STATE_VERTICAL_HEATER) == 0)
    {
        Forced_VerticalBeamHeaterState(false);
    }

    if((force & FORCE_ION_GENERATOR) && display.ion_on == false)
    {
        Vote_DeviceStatus(FSM_ForceControl, DEVICE_IonGenerator, DS_On);
        Vote_DeviceStatus(FSM_ForceControl, DEVICE_FrzIonGenerator, DS_On);
        display.ion_timer = Get_MinuteCount();
        display.ion_on = true;
    }
    else if((force & FORCE_ION_GENERATOR) == 0)
    {
        display.ion_on = false;
        Vote_DeviceStatus(FSM_ForceControl, DEVICE_IonGenerator, DS_DontCare);
        Vote_DeviceStatus(FSM_ForceControl, DEVICE_FrzIonGenerator, DS_DontCare);
    }

    if(force & FORCE_CUSTOMER_INSTALL)
    {
        FridgeState_Update((FridgeState_t)eFridge_CustomerInstall);
        force &= ~FORCE_CUSTOMER_INSTALL;
    }

    if(force != *(uint8_t *)data)
    {
        SetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_DISPLAY_FORCE, &force);
    }
    SetNfcPropertyValue(NFC_PROPERTY_TYPE_FROCE_STATE, &force);
}

static void sync_system_state(void *data)
{
    uint8_t state = *(uint8_t *)data;
    FridgeState_Update(state);
}

static bool update_door_state(void)
{
    uint16_t state = 0;

    if(Get_DoorSwitchState(DOOR_REF_LEFT) == true)
    {
        state |= 1;
    }

    if(Get_DoorSwitchState(DOOR_REF_RIGHT) == true)
    {
        state |= 2;
    }

    if(Get_DoorSwitchState(DOOR_FRZ_LEFT) == true)
    {
        state |= 4;
        state |= 0x40;
    }

    if(Get_DoorSwitchState(DOOR_FRZ_RIGHT) == true)
    {
        state |= 8;
        state |= 0x40;
    }

    if(display_param.door_state != state)
    {
        display_param.door_state = state;
        return true;
    }
    return false;
}

static bool update_comp_error_state(void)
{
    uint8_t state;

    state = Get_CompErrorState();
    if(display_param.comperror_state != state)
    {
        display_param.comperror_state = state;
        return true;
    }
    return false;
}

static bool update_wifi_state(void)
{
    uint8_t state = 0;
    FridgeState_t fstate;
    bool b_WifiConnected = false;

    fstate = Get_FridgeState();
    b_WifiConnected = Get_WifiFactoryConnected();

    state = get_dev_net_state();
    if(fstate == eFridge_Factory && b_WifiConnected)
    {
        state = ZM_APP_NET_STATE_LOCAL;
    }

    if(display_param.wifi_state != state)
    {
        display_param.wifi_state = state;
        return true;
    }
    return false;
}

static bool update_wifi_match(void)
{
    uint8_t match;

    if(is_wifi_arch_platform())
    {
        match = 0;
    }
    else
    {
        match = 1;
    }
    if(display_param.wifi_match != match)
    {
        display_param.wifi_match = match;
        return true;
    }
    return false;
}

static bool update_door_alarm(void)
{
    uint8_t door_alarm = 0;
    uint8_t DoorStateRL = Get_DoorSwitchflagState(DOOR_REF_LEFT);
    uint8_t DoorStateRR = Get_DoorSwitchflagState(DOOR_REF_RIGHT);
    uint8_t DoorStateFL = Get_DoorSwitchflagState(DOOR_FRZ_LEFT);
    uint8_t DoorStateFR = Get_DoorSwitchflagState(DOOR_FRZ_RIGHT);

    if(DoorStateRL & 0x08)
    {
        door_alarm |= 1 << 1;
    }

    if(DoorStateRR & 0x08)
    {
        door_alarm |= 1 << 2;
    }

    if(DoorStateFL & 0x08)
    {
        door_alarm |= 1 << 3;
    }

    if(DoorStateFR & 0x08)
    {
        door_alarm |= 1 << 4;
    }

    if(display_param.door_alarm != door_alarm)
    {
        display_param.door_alarm = door_alarm;
        return true;
    }
    return false;
}

static bool update_fault_code(void)
{
    uint32_t error = 0;
    uint32_t error0 = Get_FaultCodeByte(eFCode_IotByte0);
    uint32_t error1 = Get_FaultCodeByte(eFCode_IotByte1);
    uint32_t error2 = Get_FaultCodeByte(eFCode_IotByte2);
    uint32_t error3 = Get_FaultCodeByte(eFCode_IotByte3);

    error = error0 + (error1 << 8) + (error2 << 16) + (error3 << 24);
    if(display_param.error != error)
    {
        display_param.error = error;
        return true;
    }
    return false;
}

static bool update_system_state(void)
{
    uint8_t state;

    state = Get_FridgeState();
    if(display_param.system_state != state)
    {
        display_param.system_state = state;
        return true;
    }
    return false;
}

static bool update_cooling_state(void)
{
    uint8_t state;

    state = Get_CoolingCapacityState();
    if(display_param.cooling_state != state)
    {
        display_param.cooling_state = state;
        return true;
    }
    return false;
}

static bool update_hearter_state(void)
{
    uint8_t state = 0;

    if(Get_VerticalBeamHeaterDeviceState() == true)
    {
        state |= 1 << 0;
    }

    if(Get_DefrostHeaterState() == true)
    {
        state |= 1 << 1;
    }

    if(display_param.hearter_state != state)
    {
        display_param.hearter_state = state;
        return true;
    }
    return false;
}

static bool update_deforst_state(void)
{
    uint8_t dstate;
    RunningState_t state = Get_RunningState();

    dstate = (state == eRunning_Defrosting) ? 1 : 0;
    if(display_param.deforst_state != dstate)
    {
        display_param.deforst_state = dstate;
        return true;
    }
    return false;
}

static bool update_deforst_sensor(void)
{
    int8_t temp;

    temp = (Get_SensorValue(SENSOR_DEFROST) - 500) / 10;
    if(display_param.deforst_sensor != temp)
    {
        display_param.deforst_sensor = temp;
        return true;
    }
    return false;
}

static bool update_refdamper_state(void)
{
    uint8_t temp;

    temp = Get_RefDamperState();
    if(display_param.refdamper_state != temp)
    {
        display_param.refdamper_state = temp;
        return true;
    }
    return false;
}

static bool update_frzfan_state(void)
{
    uint8_t temp = Get_FanDuty(FRZ_FAN);
	
    if (temp >= 30)
    {
        temp = temp / 5 - 5;
    }
    if(display_param.frzfan_state != temp)
    {
        display_param.frzfan_state = temp;
        return true;
    }
    return false;
}

static bool update_comp_state(void)
{
    uint8_t temp;

    temp = Get_CompFreq();
    if(display_param.comp_state != temp)
    {
        display_param.comp_state = temp;
        return true;
    }
    return false;
}

static bool update_coolfan_state(void)
{
    uint8_t temp = Get_FanDuty(COOL_FAN);
	
    if (temp >= 30)
    {
        temp = temp / 5 - 5;
    } 
    if(display_param.coolfan_state != temp)
    {
        display_param.coolfan_state = temp;
        return true;
    }
    return false;
}

static bool update_reffan_state(void)
{
    uint8_t temp = Get_FanDuty(REF_FAN);
	
    if (temp >= 30)
    {
        temp = temp / 5 - 5;
    }
    if(display_param.reffan_state != temp)
    {
        display_param.reffan_state = temp;
        return true;
    }
    return false;
}

static bool update_vardamper_state(void)
{
    uint8_t temp;

    temp = Get_VarDamperState();
    if(display_param.vardamper_state != temp)
    {
        display_param.vardamper_state = temp;
        return true;
    }
    return false;
}

static bool update_humidity(void)
{
    uint8_t temp;

    temp = Get_HumidityRange();
    if(display_param.humidity != temp)
    {
        display_param.humidity = temp;
        return true;
    }
    return false;
}

static bool update_room_temp(void)
{
    int8_t temp;

    temp = (Get_SensorValue(SENSOR_ROOM) - 500) / 10;
    if(display_param.room_temp != temp)
    {
        display_param.room_temp = temp;
        return true;
    }
    return false;
}

static bool update_refdeforst_temp(void)
{
    int8_t temp;

    temp = (Get_SensorValue((SensorType_t)SENSOR_REF_DEFROST) - 500) / 10;
    if(display_param.refdeforst_temp != temp)
    {
        display_param.refdeforst_temp = temp;
        return true;
    }
    return false;
}

static bool update_valve_state(void)
{
    uint8_t u8_device_state = Get_ValveState();
    
    if(display_param.valve_state != u8_device_state)
    {
        display_param.valve_state = u8_device_state;
        return true;
    }
    return false;
}

static bool update_machine_type(void)
{
    uint8_t mt = GetMachineType();

    if(mt == MACHINE_TYPE_UNKOWN)
    {
        return false;
    }

    if(display_param.mtype != mt)
    {
        display.mtype_timer = Get_MinuteCount();
        display_param.mtype = mt;
        return true;
    }

    if(Get_MinuteElapsedTime(display.mtype_timer) > DISPLAY_MTYPE_SYNC_MINUTES)
    {
        display.mtype_timer = Get_MinuteCount();
        return true;
    }
    return false;
}

static bool update_pfault_state(void)
{
    uint32_t pfault = 0;
    bool err_state = Get_SensorError((uint8_t)SENSOR_HUMIDITY);

    if(err_state)
    {
        pfault |= 1 << 0;
    }

    if(display_param.pfault != pfault)
    {
        display_param.pfault = pfault;
        return true;
    }
    return false;
}

static bool update_ion_state(void)
{
    uint8_t state = 0;
    if(Get_IonGeneratorState())
    {
        state = 1;
    }

    if(display_param.ref_ionstate != state)
    {
        display_param.ref_ionstate = state;
        return true;
    }
    return false;
}

static bool update_frzion_state(void)
{
    uint8_t state = 0;
    if(Get_FrzIonGeneratorState())
    {
        state = 1;
    }

    if(display_param.frz_ionstate != state)
    {
        display_param.frz_ionstate = state;
        return true;
    }
    return false;
}

static void sync_food_alarm(void *data)
{
    SetNfcPropertyValue(NFC_PROPERTY_TYPE_FOOD_ALARM, data);
}

fireware_property_st display_propertys[DISPLAY_PROPERTY_TYPE_MAX] = {
    { PROPERTY_VAL_UINT8, 250, 250, &display_param.mtype, PROPERTY_DIR_OUT | PROPERTY_ALLDIRITY_FILTER, false, NULL, NULL, update_machine_type, false, 0 },
    { PROPERTY_VAL_UINT32, 2, 1, &display_param.error, PROPERTY_DIR_OUT, false, NULL, NULL, update_fault_code, false, 0 },
    { PROPERTY_VAL_UINT8, 2, 2, &display_param.mode, PROPERTY_DIR_INOUT, false, sync_mode, NULL, update_mode, false, 0 },
    { PROPERTY_VAL_UINT8, 2, 3, &display_param.door_alarm, PROPERTY_DIR_OUT, false, NULL, NULL, update_door_alarm, false, 0 },
    { PROPERTY_VAL_INT8, 3, 1, &display_param.ref_temp, PROPERTY_DIR_OUT, false, NULL, NULL, update_ref_temp, false, 0 },
    { PROPERTY_VAL_INT8, 3, 2, &display_param.ref_set_temp, PROPERTY_DIR_INOUT, false, sync_ref_set, NULL, update_ref_set, false, 0 },
    { PROPERTY_VAL_UINT8, 3, 3, &display_param.ref_disable, PROPERTY_DIR_INOUT, false, sync_ref_disable, NULL, update_ref_disable, false, 0 },
    { PROPERTY_VAL_INT8, 4, 1, &display_param.frz_temp, PROPERTY_DIR_OUT, false, NULL, NULL, update_frz_temp, false, 0 },
    { PROPERTY_VAL_INT8, 4, 2, &display_param.frz_set_temp, PROPERTY_DIR_INOUT, false, sync_frz_set, NULL, update_frz_set, false, 0 },
    { PROPERTY_VAL_INT8, 6, 1, &display_param.var_temp, PROPERTY_DIR_OUT, false, NULL, NULL, update_var_temp, false, 0 },
    { PROPERTY_VAL_UINT8, 6, 3, &display_param.infant_mode, PROPERTY_DIR_INOUT, false, sync_infant_mode, NULL, update_infant_mode, false, 0 },
    { PROPERTY_VAL_INT8, 6, 4, &display_param.vbottom_temp, PROPERTY_DIR_OUT, false, NULL, NULL, update_vbottom_temp, false, 0 },
    { PROPERTY_VAL_INT8, 6, 5, &display_param.vtop_temp, PROPERTY_DIR_OUT, false, NULL, NULL, update_vtop_temp, false, 0 },
    { PROPERTY_VAL_INT8, 6, 6, &display_param.vbottomx_temp, PROPERTY_DIR_OUT, false, NULL, NULL, update_vbottomx_temp, false, 0 },
    { PROPERTY_VAL_UINT8, 21, 2, &display_param.icemaker_test, PROPERTY_DIR_INOUT, false, sync_icemaker_test, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 8, 1, &display_param.icemaker_mode, PROPERTY_DIR_INOUT, false, sync_icemaker, NULL, update_icemaker_mode, false, 0 },
    { PROPERTY_VAL_UINT8, 8, 3, &display_param.icemaker_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_icemaker_state, false, 0 },
    { PROPERTY_VAL_UINT32, 8, 4, &display_param.icemaker_reserve, PROPERTY_DIR_INOUT, false, sync_icemaker_reserve, NULL, update_icemaker_reserve, false, 0 },
    { PROPERTY_VAL_UINT8, 8, 5, &display_param.icemaker_volume, PROPERTY_DIR_INOUT, false, sync_icemaker_volume, NULL, update_icemaker_volume, false, 0 },
    { PROPERTY_VAL_UINT8, 8, 6, &display_param.icemaker_reserve_sw, PROPERTY_DIR_INOUT, false, sync_icemaker_reserve_sw, NULL, update_icemaker_reserve_sw, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 1, &display_param.deforst_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_deforst_state, false, 0 },
    { PROPERTY_VAL_INT8, 10, 2, &display_param.deforst_sensor, PROPERTY_DIR_OUT, false, NULL, NULL, update_deforst_sensor, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 3, &display_param.refdamper_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_refdamper_state, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 4, &display_param.frzfan_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_frzfan_state, false, 0 },
    { PROPERTY_VAL_UINT16, 10, 5, &display_param.comp_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_comp_state, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 6, &display_param.coolfan_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_coolfan_state, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 8, &display_param.vardamper_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_vardamper_state, false, 0 },
    { PROPERTY_VAL_INT8, 10, 9, &display_param.room_temp, PROPERTY_DIR_OUT, false, NULL, NULL, update_room_temp, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 10, &display_param.humidity, PROPERTY_DIR_OUT, false, NULL, NULL, update_humidity, false, 0 },
    { PROPERTY_VAL_UINT16, 10, 11, &display_param.door_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_door_state, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 12, &display_param.comperror_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_comp_error_state, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 13, &display_param.system_state, PROPERTY_DIR_INOUT, false, sync_system_state, NULL, update_system_state, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 17, &display_param.cooling_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_cooling_state, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 19, &display_param.reffan_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_reffan_state, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 30, &display_param.hearter_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_hearter_state, false, 0 },
    { PROPERTY_VAL_UINT32, 10, 36, &display_param.pfault, PROPERTY_DIR_OUT, false, NULL, NULL, update_pfault_state, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 37, &display_param.valve_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_valve_state, false, 0 },
    { PROPERTY_VAL_INT8, 10, 43, &display_param.refdeforst_temp, PROPERTY_DIR_OUT, false, NULL, NULL, update_refdeforst_temp, false, 0 },
    { PROPERTY_VAL_UINT8, 11, 1, &display_param.wifi_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_wifi_state, false, 0 },
    { PROPERTY_VAL_UINT8, 11, 3, &display_param.wifi_restore, PROPERTY_DIR_INOUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 11, 4, &display_param.wifi_factory, PROPERTY_DIR_INOUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 11, 5, &display_param.wifi_match, PROPERTY_DIR_OUT, false, NULL, NULL, update_wifi_match, false, 0 },
    { PROPERTY_VAL_UINT8, 20, 4, &display_param.force_state, PROPERTY_DIR_INOUT, false, sync_force_state, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 20, 7, &display_param.test_state, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 20, 8, &display_param.inspection, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 7, 1, &display_param.recovery, PROPERTY_DIR_INOUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 3, 4, &display_param.ref_ionstate, PROPERTY_DIR_OUT, false, NULL, NULL, update_ion_state, false, 0 },
    { PROPERTY_VAL_UINT8, 4, 5, &display_param.frz_ionstate, PROPERTY_DIR_OUT, false, NULL, NULL, update_frzion_state, false, 0 },
    { PROPERTY_VAL_UINT8, 9, 1, &display_param.food_alarm, PROPERTY_DIR_IN, false, sync_food_alarm, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 13, 4, &display_param.food_unalarm, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT16, 13, 5, &display_param.food_alarm_room, PROPERTY_DIR_OUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_INT16, 13, 6, &display_param.food_alarm_days, PROPERTY_DIR_OUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 7, 2, &display_param.init, PROPERTY_DIR_INOUT, false, NULL, sync_poweron, NULL, false, 0 },
};

static int32_t Display_recvframe(fireware_frame_st *data)
{
    uint8_t *rdata;
    uint16_t rlen = 0;
    fireware_property_st *prop;
    uint8_t index;

    if(display.poll_count == 0)
    {
        display.poll_count = DISPLAY_EVENT_POLL_TIMER_MS;
        if(Is_Sbus_Slave_Err(&display.slave) == true)
        {
            display.poll_count = DISPLAY_ERROR_EVENT_POLL_TIMER_MS;
        }
    }

    if(NULL == data)
    {
        if(display.dirty && display.frame.fb1 == FIREWARE_PROPERTY_FUNC && display.frame.fb2 == PROPERTY_SUBFUNC_IOT)
        {
            display.dirty = false;
            if(display.init == false)
            {
                display.b_appversion = false;
                display.alldirty = true;
            }
        }
        return -1;
    }
    else
    {
        if(data->srcAddr != FIREWARE_DISPLAY_ADDR)
        {
            err("(%d, %d)not display frame\n", data->srcAddr, data->fb1);
            return -1;
        }

        if(data->fb1 == FIREWARE_PROPERTY_FUNC)
        {
            if(data->fb2 == PROPERTY_SUBFUNC_QUERY_RESPONSE)
            {
                if(data->len > 0)
                {
                    rdata = Parse_PropertyData(data->data, data->len, &rlen, display_propertys, DISPLAY_PROPERTY_TYPE_MAX);
                    if(rlen != 0)
                    {
                        Edit_Property_ResponseFrame(FIREWARE_DISPLAY_ADDR, &display.frame, rdata, rlen);
                        Sbus_Slave_Request(&display.slave, SBUS_PKT_PRI_LEVEL7);
                        display.response = true;
                        display.dirty = false;
                    }
                }
                if(display_param.init == 1)
                {
                    display.b_bootversion = false;
                    display.b_appversion = false;
                    display_param.init = 0;
                    display_param.mtype = MACHINE_TYPE_UNKOWN;
                    display.init = false;
                    display.dirty = false;
                    display.alldirty = true;
                }
            }
            else if(data->fb2 == PROPERTY_SUBFUNC_RESPONSE)
            {
                if(data->len > 0)
                {
                    Parse_PropertyResponse(data->data, data->len, display_propertys, DISPLAY_PROPERTY_TYPE_MAX);
                }
                display.dirty = false;
            }
        }
        else if(data->fb1 == FIREWARE_OTA_FUNC)
        {
            if(data->fb2 == FIREWARE_OTA_QUERY_VER)
            {
                display.hwversion = data->data[0] << 8 | data->data[1];
                display.appversion = data->data[2] << 8 | data->data[3];
                display.appcrc = data->data[4] << 8 | data->data[5];
                display.b_appversion = true;
            }

            if(data->fb2 == FIREWARE_OTA_BOOT_VER)
            {
                display.bootVersion = data->data[0] << 8 | data->data[1];
                display.bootCrc = data->data[2] << 8 | data->data[3];
                display.b_bootversion = true;
            }
        }
    }
    return 0;
}

static fireware_frame_st *Display_sendframe(sbus_pkt_pri_e pri)
{
    if(pri == SBUS_PKT_PRI_LEVEL6)
    {
        if(display.b_appversion == false)
        {
            Edit_OtaControlFrame(&display.frame,
                display.slave.addr,
                FIREWARE_OTA_QUERY_VER);
        }
        else
        {
            if(display.init == true)
            {
                Edit_Property_QueryFrame(display.slave.addr, &display.frame);
            }
            else
            {
                return NULL;
            }
        }
    }
    else if(pri == SBUS_PKT_PRI_LEVEL5)
    {
        if(display.dirty == true)
        {
            Edit_Property_Frame(display.slave.addr, &display.frame, display_propertys, DISPLAY_PROPERTY_TYPE_MAX);
        }
        else
        {
            return NULL;
        }
    }
    else if(pri == SBUS_PKT_PRI_LEVEL4)
    {
        if(display.b_bootversion == false)
        {
            Edit_OtaControlFrame(&display.frame,
                display.slave.addr,
                FIREWARE_OTA_BOOT_VER);
        }
        else
        {
            return NULL;
        }
    }
    else if(pri == SBUS_PKT_PRI_LEVEL7)
    {
        if(display.response == true)
        {
            display.response = false;
        }
        else
        {
            return NULL;
        }
    }

    return &display.frame;
}

void Init_SbusDisplay(void)
{
    uint8_t index;
    fireware_property_st *prop;

    display.slave.addr = FIREWARE_DISPLAY_ADDR;
    display.slave.ops.recvFrame = Display_recvframe;
    display.slave.ops.sendFrame = Display_sendframe;
    display.poll_count = DISPLAY_EVENT_POLL_TIMER_MS;
    display.init = false;
    display.dirty = false;
    display.alldirty = true;
    display.mtype_timer = Get_MinuteCount();
    Sbus_Slave_Register(SBUS_TYPE_ID0, &display.slave);
}

void Update_Display_Param(void)
{
    bool update;
    uint8_t index;
    fireware_property_st *prop;

    if(display.dirty)
    {
        return;
    }

    for(index = 0; index < DISPLAY_PROPERTY_TYPE_MAX; index++)
    {
        prop = &display_propertys[index];
        if((prop->flags & PROPERTY_DIR_OUT) && prop->dirty1 == true)
        {
            prop->dirty1 = false;
            prop->dirty = true;
        }
        if((prop->flags & PROPERTY_DIR_OUT) &&
            prop->update_property)
        {
            update = prop->update_property();
            if(update == true)
            {
                prop->dirty = true;
            }
        }
        if((prop->flags & PROPERTY_DIR_OUT) &&
            (prop->dirty == true || ((prop->flags & PROPERTY_ALLDIRITY_FILTER) == 0 && display.alldirty == true)))
        {
            prop->dirty = true;
            display.dirty = true;
        }
    }

    if(display.alldirty == true)
    {
        display.alldirty = false;
    }

    if(display.dirty)
    {
        Sbus_Slave_Request(&display.slave, SBUS_PKT_PRI_LEVEL5);
    }
}

void Handle_Display_Overtime(void)
{
    if(display.poll_count > 0)
    {
        display.poll_count--;
        if(display.poll_count == 0)
        {
            Sbus_Slave_Request(&display.slave, SBUS_PKT_PRI_LEVEL6);
            if(Is_Sbus_Slave_Err(&display.slave) == true)
            {
                Update_Display_Param();
            }
        }
    }

    if(Is_Sbus_Slave_Err(&display.slave) == false)
    {
        Update_Display_Param();
    }

    if(display.b_bootversion == false && display.boot_count++ >= DISPLAY_BOOT_MS)
    {
        display.boot_count = 0;
        Sbus_Slave_Request(&display.slave, SBUS_PKT_PRI_LEVEL4);
    }
}

void Handle_Display_Request()
{
    uint8_t val;

    if(display_param.wifi_restore)
    {
        display.wifi_timer++;
        if(display.wifi_timer > DISPLAY_WIFI_REQUEST_TIMEOUT_500MS || execute_wifi_cmd_async(WIFI_CMD_RESTORE, NULL) == 0)
        {
            val = 0;
            display.wifi_timer = 0;
            SetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_WIFI_RESTORE, &val);
        }
    }
    else if(display_param.wifi_factory)
    {
        display.wifi_timer++;
        if(display.wifi_timer > DISPLAY_WIFI_REQUEST_TIMEOUT_500MS || execute_wifi_cmd_async(WIFI_CMD_FACTORY, NULL) == 0)
        {
            val = 0;
            display.wifi_timer = 0;
            SetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_WIFI_FACTORY, &val);
        }
    }
    else if(display.b_reboot_wifi)
    {
        display.wifi_timer++;
        if(display.wifi_timer > DISPLAY_WIFI_REQUEST_TIMEOUT_500MS || execute_wifi_cmd_async(WIFI_CMD_REBOOT, NULL) == 0)
        {
            display.wifi_timer = 0;
            display.b_reboot_wifi = false;
        }
    }

    if(display_param.recovery)
    {
        RecoveryUsrSysParam();
        val = 0;
        SetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_RECOVERY, &val);
    }

    if(display_param.inspection)
    {
        RecoverySysParam();
        val = 0;
        SetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_INSPECTION, &val);
    }

    if(display.ion_on == true)
    {
        if(Get_MinuteElapsedTime(display.ion_timer) > ION_FORCE_ON_MIN)
        {
            uint8_t ion_force = 0;
            GetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_DISPLAY_FORCE, &ion_force);
            ion_force &= ~FORCE_ION_GENERATOR;
            SetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_DISPLAY_FORCE, &ion_force);
            Vote_DeviceStatus((uint8_t)FSM_ForceControl, DEVICE_IonGenerator, DS_DontCare);
            Vote_DeviceStatus((uint8_t)FSM_ForceControl, DEVICE_FrzIonGenerator, DS_DontCare);
            display.ion_on = false;
        }
    }
}

bool Get_WifiConnectState(void)
{
    bool b_WiFiState;
    net_state_e net_state = get_dev_net_state();

    switch(net_state)
    {
        case ZM_APP_NET_STATE_NONE:
        case ZM_APP_NET_STATE_OFFLINE:
        case ZM_APP_NET_STATE_UAP:
        case ZM_APP_NET_STATE_UNPROV:
            b_WiFiState = false;
            break;
        case ZM_APP_NET_STATE_LOCAL:
        case ZM_APP_NET_STATE_UPDATING:
        case ZM_APP_NET_STATE_UPDATING_AUTO:
        case ZM_APP_NET_STATE_UPDATING_FORCE:
        case ZM_APP_NET_STATE_CLOUD:
        default:
            b_WiFiState = true;
            break;
    }

    return b_WiFiState;
}

__EMULATOR__FUNCITON bool Get_DisplayCommErr(void)
{
    return (Is_Sbus_Master_Err(&display.slave) || Is_Sbus_Slave_Err(&display.slave));
}

int8_t GetDisplayPropertyValue(display_property_type_e type, void *data)
{
    if(type >= DISPLAY_PROPERTY_TYPE_MAX || data == NULL)
    {
        return FIREWARE_ERROR;
    }
    memcpy(data, display_propertys[type].data, get_Property_Size(display_propertys[type].val));
    return FIREWARE_SUCCESS;
}

int8_t SetDisplayPropertyValue(display_property_type_e type, void *data)
{
    if(type >= DISPLAY_PROPERTY_TYPE_MAX || data == NULL)
    {
        return FIREWARE_ERROR;
    }
    memcpy(display_propertys[type].data, data, get_Property_Size(display_propertys[type].val));
    if(display.dirty == true)
    {
        display_propertys[type].dirty1 = true;
    }
    else
    {
        display_propertys[type].dirty = true;
    }
    return FIREWARE_SUCCESS;
}

bool GetDisplayPropertyState(display_property_type_e type)
{
    if(type >= DISPLAY_PROPERTY_TYPE_MAX)
    {
        return false;
    }

    return (display_propertys[type].dirty || display_propertys[type].dirty1);
}

uint8_t Get_TestMode(void)
{
    return display_param.test_state;
}

uint32_t Get_DisplayBootVersion(void)
{
    if(display.b_bootversion)
    {
        return display.bootVersion;
    }

    return 0;
}

uint32_t Get_DisplayBootCrc(void)
{
    if(display.b_bootversion)
    {
        return display.bootCrc;
    }

    return 0;
}

uint32_t Get_DisplayAppVersion(void)
{
    if(display.b_appversion)
    {
        return display.appversion;
    }

    return 0;
}

uint32_t Get_DisplayAppCrc(void)
{
    if(display.b_appversion)
    {
        return display.appcrc;
    }

    return 0;
}

sbus_slave_stats *Get_DisplayPacketStats(void)
{
    return &display.slave.stats;
}

