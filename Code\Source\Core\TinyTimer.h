/*!
 * @file
 * @brief
 *
 * Copyright xxxx - Confidential - All rights reserved.
 */

#ifndef TINYTIMER_H
#define TINYTIMER_H

#include "TinyLinkedList.h"
#include <stdbool.h>
#include <stdint.h>

typedef uint16_t TinyTimeSourceTickCount_t;

struct I_TinyTimeSource_Api_t;

typedef struct
{
    const struct I_TinyTimeSource_Api_t *api;
} I_TinyTimeSource_t;

typedef struct I_TinyTimeSource_Api_t
{
    /*!
     * @param instance.
     * @return The current tick count.
     */
    TinyTimeSourceTickCount_t (*GetTicks)(I_TinyTimeSource_t *instance);
} I_TinyTimeSource_Api_t;

#define TinyTimeSource_GetTicks(instance) (instance)->api->GetTicks((instance))

typedef uint16_t TinyTimerTicks_t;

struct TinyTimerModule_t;

typedef void (*TinyTimerCallback_t)(void);

typedef struct
{
    struct
    {
        TinyLinkedListNode_t node;
        TinyTimerCallback_t callback;
        void *context;
        TinyTimerTicks_t remainingTicks;
        TinyTimerTicks_t startTicks; // Store original tick duration
        bool autoReload; // Auto-reload flag for periodic timers
    } _private;
} TinyTimer_t;

typedef struct TinyTimerModule_t
{
    struct
    {
        I_TinyTimeSource_t *timeSource;
        TinyLinkedList_t timers;
        TinyTimerTicks_t lastTicks;
    } _private;
} TinyTimerModule_t;

/*!
 * @param instance
 * @param timeSource
 */
void TinyTimerModule_Init(I_TinyTimeSource_t *timeSource);

/*!
 * Start a one-shot timer.
 * @param instance
 * @param timer
 * @param ticks
 * @param callback
 * @param context
 */
void TinyTimerModule_StartOneShot(TinyTimer_t *timer,
    TinyTimerTicks_t ticks,
    TinyTimerCallback_t callback,
    void *context);

/*!
 * Start a periodic timer.
 * @param instance
 * @param timer
 * @param ticks
 * @param callback
 * @param context
 */
void TinyTimerModule_StartPeriodic(TinyTimer_t *timer,
    TinyTimerTicks_t ticks,
    TinyTimerCallback_t callback,
    void *context);

/*!
 * Stops a timer. Can be called even if the timer isn't running.
 * @param instance
 * @param timer
 */
void TinyTimerModule_Stop(TinyTimer_t *timer);

/*!
 * The remaining ticks for a running timer. The result is only valid
 * if the timer is running.
 */
TinyTimerTicks_t TinyTimerModule_RemainingTicks(TinyTimer_t *timer);

/*!
 * @return True if at least one timer was called back, false otherwise.
 */
bool TinyTimerModule_Run(void);

#endif
