/*!
 * @file
 * @brief Enables timers to be used with callback functions and provides a way for
 * timers to be used without having to poll them.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __Core_CallbackTimer_H__
#define __Core_CallbackTimer_H__

#include <stdbool.h>
#include "Core_Types.h"
#include "TinyTimer.h"
#include "CoreUser_CallBackTimer_Config.h"

#ifndef NUM_ELEMENTS

#define NUM_ELEMENTS(array) (sizeof(array) / sizeof(array[0]))
#define ELEMENT_COUNT NUM_ELEMENTS
#define ELEMENT_SIZE(array) (sizeof(array[0]))

#endif

#if(true == CALLBACK_TIMER_CALLBACK_DATA_ENABLED)
typedef void (*fpTimerCallBackFunction)(void *);
#else
typedef void (*fpTimerCallBackFunction)(void);
#endif

typedef enum
{
    eCallbackTimer_Type_OneShot = 0,
    eCallbackTimer_Type_Periodic,
    eCallbackTimer_Type_Max
} EN_Core_CallbackTimer_Type;

typedef enum
{
    eCallbackTimer_Priority_Normal = 0,
    eCallbackTimer_Priority_High,
    eCallbackTimer_Priority_Max
} EN_Core_CallbackTimer_Priority;

typedef struct
{
    TinyTimer_t st_Timer;
    fpTimerCallBackFunction fp_CallBackFunction;
#if(true == CALLBACK_TIMER_CALLBACK_DATA_ENABLED)
    void *p_CallBackData;
#endif
    uint16_t u16_DurationSeconds;
    uint16_t u16_DurationMilliSeconds;
    uint8_t u8_TimerType;
    uint8_t u8_Priority;
} st_CoreCallbackTimer;

/*!
 * @brief Used to add a timer and its corresponding callback function to the pool of
 * timers that it being serviced by the TimerCallBack module.
 * @param st_CoreCallbackTimer * pst_CallBackTimer : The timer
 * @param fpTimerCallBackFunction fp_CallBackFunction : Function called upon expiration of the timer
 * @param void * p_CallbackData : Data sent to callback function on expiration of the timer
 * @param uint16_t u16_DurationSeconds, uint16_t u16_DurationMilliSeconds : Time until expiration of the timer
 * @param EN_Core_CallbackTimer_Type en_TimerType : Type (one-shot or periodic) of the timer One-shot
 * timers are removed from the pool after expiration. Periodic timers are reloaded on expiration.
 * @param EN_Core_CallbackTimer_Priority en_Priority : Priority (high or normal) of the timer
 * @retval true if all init parameters are valid, false otherwise.
 */
extern void Core_CallbackTimer_TimerStart(
    st_CoreCallbackTimer *pst_CallBackTimer,
    fpTimerCallBackFunction fp_CallBackFunction,
#if(true == CALLBACK_TIMER_CALLBACK_DATA_ENABLED)
    void *p_CallBackData,
#endif
    uint16_t u16_DurationSeconds,
    uint16_t u16_DurationMilliSeconds,
    EN_Core_CallbackTimer_Type en_TimerType,
    EN_Core_CallbackTimer_Priority en_Priority);

/*!
 * @brief Removes a timer and its corresponding callback functions from the pool
 * of timers serviced by the TimerCallback module.
 * @param st_CoreCallbackTimer * pst_CallBackTimer : The timer
 */
extern void Core_CallbackTimer_TimerStop(
    st_CoreCallbackTimer *pst_CallBackTimer);

/*!
 * @brief Get the time remaining if a timer is running.  If timer not running,
 * does not modify the seconds or milliseconds arguments.
 * @param st_CoreCallbackTimer * pst_CallBackTimer : The timer
 * @param uint16_t * pu16_Seconds, uint16_t *pu16_Milliseconds : Pointers to where the
 * seconds and milliseconds will be
 */
extern void Core_CallbackTimer_TimeRemaining(
    st_CoreCallbackTimer *pst_CallBackTimer, uint16_t *pu16_Seconds, uint16_t *pu16_Milliseconds);

#endif
