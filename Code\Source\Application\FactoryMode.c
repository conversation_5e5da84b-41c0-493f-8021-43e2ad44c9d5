/*!
 * @file
 * @brief Manages all the state variables of the factory mode.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "FactoryMode.h"
#include "Core_CallBackTimer.h"
#include "ResolverDevice.h"
#include "Driver_GradualLamp.h"
#include "IO_Device.h"
#include "Driver_SingleDamper.h"
#include "Driver_CompFrequency.h"
#include "SystemManager.h"
#include "Driver_Fan.h"
#include "DisplayInterface.h"
#include "Driver_AdSample.h"
#include "miio_api.h"
#include "FridgeRunner.h"
#include "Sbus_Display.h"
#include "Sbus_IceMaker.h"
#include "Drive_Valve.h"
#include "InverterUsart.h"
#include "ParameterManager.h"
#include "Driver_DoorSwitch.h"
#include "Driver_Flash.h"
#include "FaultCode.h"
#include "Iot_Spec.h"
#include "miio_api.h"

#define U16_SELF_CHECK_CYCLE_SECOND (uint16_t)1

static st_CoreCallbackTimer st_FactoryModeTimer;
static uint16_t u16_ControlCount;
static uint16_t fct_room_temp = 0;
static bool b_FctCtrlLoadOver;
static bool b_FctWriteSnSuccess;
static bool b_WifiFactory;
static bool b_WifiReboot;
static char factory_data[FACTORY_DATA_BUFFER];
static bool b_WifiConnected = false;
static bool b_WiFiMatch = false;
static bool b_WiFiStateUpdate = true;
static bool b_WifiFactoryResponsed = false;
static bool b_WifiRebootResponsed = false;
static machine_type_e mtype = MACHINE_TYPE_UNKOWN;
static machine_capacity_e mcapacity = MACHINE_CAPACITY_NORMAL;
static uint8_t u8_mtype_timeout = 0;

static void check_fault_code(void *data)
{
    uint32_t *perror = (uint32_t *)data;
    uint32_t error0 = Get_FaultCodeByte(eFCode_IotByte0);
    uint32_t error1 = Get_FaultCodeByte(eFCode_IotByte1);
    uint32_t error2 = Get_FaultCodeByte(eFCode_IotByte2);
    uint32_t error3 = Get_FaultCodeByte(eFCode_IotByte3);

    *perror = error0 + (error1 << 8) + (error2 << 16) + (error3 << 24);
}

static void check_door_state(void *data)
{
    uint8_t *door_alarm = (uint8_t *)data;
    uint8_t DoorStateRL = Get_DoorSwitchflagState(DOOR_REF_LEFT);
    uint8_t DoorStateRR = Get_DoorSwitchflagState(DOOR_REF_RIGHT);
    uint8_t DoorStateFL = Get_DoorSwitchflagState(DOOR_FRZ_LEFT);
    uint8_t DoorStateFR = Get_DoorSwitchflagState(DOOR_FRZ_RIGHT);

    *door_alarm = 0;

    if(DoorStateRL & 0x08)
    {
        *door_alarm |= 1 << 1;
    }

    if(DoorStateRR & 0x08)
    {
        *door_alarm |= 1 << 2;
    }

    if(DoorStateFL & 0x08)
    {
        *door_alarm |= 1 << 3;
    }

    if(DoorStateFR & 0x08)
    {
        *door_alarm |= 1 << 4;
    }
}

static void check_ref_temp(void *data)
{
    uint16_t temp;
    uint16_t *rt = (uint16_t *)data;

    temp = Get_SensorValue(SENSOR_REF);
    *rt = temp;
}

static void check_frz_temp(void *data)
{
    uint16_t temp;
    uint16_t *rt = (uint16_t *)data;

    temp = Get_SensorValue(SENSOR_FRZ);
    *rt = temp;
}

static void check_var_temp(void *data)
{
    uint16_t temp;
    uint16_t *rt = (uint16_t *)data;

    temp = Get_SensorValue(SENSOR_VV);
    *rt = temp;
}

static void check_env_temp(void *data)
{
    uint16_t temp;
    uint16_t *rt = (uint16_t *)data;

    temp = Get_SensorValue(SENSOR_ROOM);
    *rt = temp;
}

static void check_env_hum(void *data)
{
    uint16_t var;
    uint8_t *rt = (uint8_t *)data;

    var = Get_HumidityRange();
    *rt = var;
}

static void check_door_switch(void *data)
{
    uint8_t *door_switch = (uint8_t *)data;
    uint8_t DoorStateRL = Get_DoorSwitchflagState(DOOR_REF_LEFT);
    uint8_t DoorStateRR = Get_DoorSwitchflagState(DOOR_REF_RIGHT);
    uint8_t DoorStateFL = Get_DoorSwitchflagState(DOOR_FRZ_LEFT);
    uint8_t DoorStateFR = Get_DoorSwitchflagState(DOOR_FRZ_RIGHT);

    *door_switch = 0;

    if(DoorStateRL & 0x01)
    {
        *door_switch |= 1 << 1;
    }

    if(DoorStateRR & 0x01)
    {
        *door_switch |= 1 << 2;
    }

    if(DoorStateFL & 0x01)
    {
        *door_switch |= 1 << 3;
    }

    if(DoorStateFR & 0x01)
    {
        *door_switch |= 1 << 4;
    }
}

static void check_comp_error(void *data)
{
    uint8_t var;
    uint8_t *rt = (uint8_t *)data;

    var = Get_CompErrorState();
    *rt = var;
}

static void check_system_state(void *data)
{
    uint8_t var;
    uint8_t *rt = (uint8_t *)data;

    var = Get_FridgeState();
    *rt = var;
}

static void check_comp_feedfreq(void *data)
{
    uint8_t var;
    uint8_t *rt = (uint8_t *)data;

    var = Get_CompFeedbackFreq();
    *rt = var;
}

static void check_comp_feedpower(void *data)
{
    uint16_t var;
    uint16_t *rt = (uint16_t *)data;

    var = Get_CompPower();
    *rt = var;
}

static void check_comp_feedvol(void *data)
{
    uint16_t var;
    uint16_t *rt = (uint16_t *)data;

    var = Get_CompBusVoltage();
    *rt = var;
}

static void check_other_fault(void *data)
{
    uint32_t var = 0;
    uint32_t *rt = (uint32_t *)data;

    if(Get_SensorError(SENSOR_HUMIDITY))
    {
        var |= 1 << 0;
    }
    *rt = var;
}

static factory_item_st fitems[] = {
    { "2_1", FACTORY_ITEM_VAL_UINT32, check_fault_code },
    { "2_3", FACTORY_ITEM_VAL_UINT8, check_door_state },
    { "3_1", FACTORY_ITEM_VAL_UINT16, check_ref_temp },
    { "4_1", FACTORY_ITEM_VAL_UINT16, check_frz_temp },
    { "5_1", FACTORY_ITEM_VAL_UINT16, check_var_temp },
    { "10_9", FACTORY_ITEM_VAL_UINT16, check_env_temp },
    { "10_10", FACTORY_ITEM_VAL_UINT8, check_env_hum },
    { "10_11", FACTORY_ITEM_VAL_UINT8, check_door_switch },
    { "10_12", FACTORY_ITEM_VAL_UINT8, check_comp_error },
    { "10_13", FACTORY_ITEM_VAL_UINT8, check_system_state },
    { "10_20", FACTORY_ITEM_VAL_UINT8, check_comp_feedfreq },
    { "10_21", FACTORY_ITEM_VAL_UINT16, check_comp_feedpower },
    { "10_22", FACTORY_ITEM_VAL_UINT16, check_comp_feedvol },
    { "10_36", FACTORY_ITEM_VAL_UINT32, check_other_fault },
};

static void Check_Factory_Items(void)
{
    uint16_t items = sizeof(fitems) / sizeof(factory_item_st);
    uint8_t buf[PRODUCT_MODEL_SIZE + 1] = { 0 };
    factory_item_st *fit = NULL;
    uint16_t index;
    uint32_t udata32;
    uint8_t udata8;
    int8_t data8;
    uint16_t udata16;

    memset(factory_data, 0, sizeof(factory_data));

    str_n_cat(factory_data, 1, "\"");

    if(ReadProductUserModel(buf, PRODUCT_MODEL_SIZE) < 0)
    {
        goto out;
    }

    str_n_cat(factory_data, 3, "0:", buf, " ");
    for(index = 0; index < items; index++)
    {
        fit = &fitems[index];

        if(fit->type == FACTORY_ITEM_VAL_UINT32)
        {
            udata32 = 0;
            fit->check_factory_item(&udata32);
            snprintf((char *)buf, PRODUCT_MODEL_SIZE, "%u", udata32);
        }
        else if(fit->type == FACTORY_ITEM_VAL_UINT8)
        {
            udata8 = 0;
            fit->check_factory_item(&udata8);
            snprintf((char *)buf, PRODUCT_MODEL_SIZE, "%u", udata8);
        }
        else if(fit->type == FACTORY_ITEM_VAL_INT8)
        {
            data8 = 0;
            fit->check_factory_item(&data8);
            snprintf((char *)buf, PRODUCT_MODEL_SIZE, "%d", data8);
        }
        else if(fit->type == FACTORY_ITEM_VAL_UINT16)
        {
            udata16 = 0;
            fit->check_factory_item(&udata16);
            snprintf((char *)buf, PRODUCT_MODEL_SIZE, "%u", udata16);
        }

        if(index + 1 == items)
        {
            str_n_cat(factory_data, 3, fit->key, ":", buf);
        }
        else
        {
            str_n_cat(factory_data, 4, fit->key, ":", buf, " ");
        }
    }
out:
    str_n_cat(factory_data, 1, "\"");
}

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_FactoryModeTimer);
}

static void wifi_factory_response(const char *pValue, char result)
{
    if(result > 0 && b_WifiFactory == true)
    {
        b_WifiFactoryResponsed = true;
    }
    else
    {
        b_WifiFactory = false;
    }
}

static void wifi_reboot_response(const char *pValue, char result)
{
    if(result > 0 && b_WifiReboot == true)
    {
        b_WifiRebootResponsed = true;
    }
    else
    {
        b_WifiReboot = false;
    }
}

static void FactoryMode_ControlLoad(void)
{
    bool wifi_error = false;

    if(u16_ControlCount < 1)
    {
        Vote_DeviceStatus_Immediate(FSM_NormalControl, DEVICE_Comp, 0);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_RefDefHeater, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_RefFan, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_CoolFan, DS_Off);
    }
    else if(u16_ControlCount < 6)
    {
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_On);
    }
    else if(u16_ControlCount < 11)
    {
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_On);
    }
    else if(u16_ControlCount < 19)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off);
        if(u16_ControlCount == 11)
        {
            Drive_ValveForce(true);
        }
    }
    else if(u16_ControlCount < 24)
    {
        Drive_ValveForce(false);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_RefFan, 100);
    }
    else if(u16_ControlCount < 29)
    {
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_RefFan, 0);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_FrzFan, 100);
    }
    else if(u16_ControlCount < 34)
    {
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_FrzFan, 0);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_CoolFan, 100);
    }
    else if(u16_ControlCount < 58)
    {
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_CoolFan, 0);
        if(u16_ControlCount == 34)
        {
            Reset_SingleDamper(Damper_Var);
        }
    }
    else
    {
        Drive_ValveReset();
        u16_ControlCount = 0;
        b_FctCtrlLoadOver = true;
        fct_room_temp = Get_SensorValue(SENSOR_ROOM);
    }
    u16_ControlCount++;
}

static void FactoryMode_ControlLoadWithIceMaker(void)
{
    uint8_t icemaker_load = 0;

    if(u16_ControlCount < 1)
    {
        Vote_DeviceStatus_Immediate(FSM_NormalControl, DEVICE_Comp, 0);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_RefDefHeater, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_RefFan, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_CoolFan, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_VarDamperHeater, DS_Off);
        SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
    }
    else if(u16_ControlCount < 5)
    {
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_On);
    }
    else if(u16_ControlCount < 9)
    {
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_RefDefHeater, DS_On);
    }
    else if(u16_ControlCount < 13)
    {
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_RefDefHeater, DS_Off);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_On);
    }
    else if(u16_ControlCount < 21)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off);
        if(u16_ControlCount == 13)
        {
            Drive_ValveForce(true);
        }
    }
    else if(u16_ControlCount < 25)
    {
        icemaker_load = 0x01;
        Drive_ValveForce(false);
        SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
    }
    else if(u16_ControlCount < 29)
    {
        icemaker_load = 0x03;
        SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
    }
    else if(u16_ControlCount < 34)
    {
        icemaker_load = 0x06;
        SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
    }
    else if(u16_ControlCount < 39)
    {
        icemaker_load = 0x02;
        SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
    }
    else if(u16_ControlCount < 44)
    {
        SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_RefFan, 100);
    }
    else if(u16_ControlCount < 49)
    {
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_RefFan, 0);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_FrzFan, 100);
    }
    else if(u16_ControlCount < 54)
    {
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_FrzFan, 0);
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_CoolFan, 100);
    }
    else if(u16_ControlCount < 58)
    {
        Vote_DeviceStatus_Immediate(FSM_SpecialModes, DEVICE_CoolFan, 0);
        if(u16_ControlCount == 54)
        {
            Reset_SingleDamper(Damper_Var);
        }
    }
    else
    {
        Drive_ValveReset();
        u16_ControlCount = 0;
        b_FctCtrlLoadOver = true;
        fct_room_temp = Get_SensorValue(SENSOR_ROOM);
    }
    u16_ControlCount++;
}

static void FactoryMode_ControlCooling(void)
{
    if(u16_ControlCount < (24 * 60))
    {
        if((u16_ControlCount % 60) < 18)
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_On);
        }
        else
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off);
        }
    }

    if(u16_ControlCount < (5 * 60))
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 30);
    }
    else if(u16_ControlCount < (5 * 60 + 1))
    {
        if(fct_room_temp > CON_32P0_DEGREE)
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 30);
        }
        else
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_Off);
        }
    }

    if(u16_ControlCount < (7 * 60))
    {
        if(u16_ControlCount < 20)
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_FrzON_RefOFF);
        }
        else
        {
            Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, FREQ_108HZ);
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 0);
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 69);
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DAMPER_AllClose);
            if((mcapacity & MACHINE_CAPACITY_ICEMAKER) &&
                u16_ControlCount == 24)
            {
                uint8_t icemaker_load = 0xFF;
                SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
            }
        }
    }
    else if(u16_ControlCount < (14 * 60))
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_FrzON_RefOFF);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, FREQ_108HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 0);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 69);
        if(u16_ControlCount > (9 * 60))
        {
            b_WiFiStateUpdate = false;
        }

        if(b_WiFiStateUpdate || (b_WifiConnected && b_WiFiMatch))
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DS_On);
        }
        else
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DAMPER_AllClose);
        }
    }
    else if(u16_ControlCount < (21 * 60))
    {
        if(u16_ControlCount == 17 * 60)
        {
            Set_FactoryEntryNumber(1);
            SetSysParam(SYSPARAM_INSPECTION, 1);
        }
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_FrzOFF_RefON);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, FREQ_108HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 69);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 0);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DAMPER_AllClose);
    }
    else
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, DS_DontCare);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, FREQ_108HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDefHeater, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DS_DontCare);

        if(b_WifiRebootResponsed == false)
        {
            if(b_WifiReboot == false)
            {
                if(execute_wifi_cmd_async(WIFI_CMD_REBOOT, wifi_reboot_response) == 0)
                {
                    b_WifiReboot = true;
                }
            }
        }

        if(u16_ControlCount > 22 * 60 || b_WifiRebootResponsed == true)
        {
            Stop_PollTimer();
            Update_RefVarSetTemp(eRefVar_FreshMeat);
            Set_UserMode(eManual_Mode);
            Set_CoolingEntryMode(eMode_FactoryCompleted);
            FridgeState_Update(eFridge_Running);
        }
    }
    u16_ControlCount++;
}

static void Process_FactoryMode(void)
{
    if(b_FctWriteSnSuccess == true)
    {
        if(mtype == MACHINE_TYPE_UNKOWN && u8_mtype_timeout < 3)
        {
            mtype = GetMachineType();
            if(mtype == MACHINE_TYPE_UNKOWN)
            {
                u8_mtype_timeout++;
                return;
            }
            mcapacity = GetMachineCapacity();
        }

        if(false == b_FctCtrlLoadOver)
        {
            if(mcapacity & MACHINE_CAPACITY_ICEMAKER)
            {
                FactoryMode_ControlLoadWithIceMaker();
            }
            else
            {
                FactoryMode_ControlLoad();
            }
        }
        else
        {
            FactoryMode_ControlCooling();
        }
    }
    else
    {
#ifndef USER_MODEL
        if(IsParameterManagerReady())
        {
            b_FctWriteSnSuccess = true;
        }
#else
        b_FctWriteSnSuccess = true;
#endif
    }

#ifndef USER_MODEL
    Check_Factory_Items();
#endif

    if(b_WiFiStateUpdate)
    {
        if(b_WiFiMatch == false)
        {
            b_WiFiMatch = is_wifi_arch_platform();
        }

        if(b_WifiFactoryResponsed == false)
        {
            if(b_WifiFactory == false)
            {
                if(execute_wifi_cmd_async(WIFI_CMD_FACTORY, wifi_factory_response) == 0)
                {
                    b_WifiFactory = true;
                }
            }
        }
        else if(b_WifiConnected == false)
        {
            b_WifiConnected = Get_WifiConnectState();
        }
#ifdef USER_MODEL
        else if(b_WifiRebootResponsed == false)
        {
            if(b_WifiReboot == false)
            {
                if(execute_wifi_cmd_async(WIFI_CMD_REBOOT, wifi_reboot_response) == 0)
                {
                    b_WifiReboot = true;
                }
            }
        }
#endif
    }
}

static void Start_PollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_FactoryModeTimer,
        Process_FactoryMode,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

void FactoryMode_Init(void)
{
    fct_room_temp = Get_SensorValue(SENSOR_ROOM);
    u8_mtype_timeout = 0;
    b_FctCtrlLoadOver = false;
    u16_ControlCount = 0;
    b_WifiReboot = false;
    b_WiFiStateUpdate = true;
    b_WifiConnected = false;
    b_WiFiMatch = false;
    b_WifiFactory = false;
    b_FctWriteSnSuccess = false;
    b_WifiRebootResponsed = false;
    b_WifiFactoryResponsed = false;
    Start_PollTimer(U16_SELF_CHECK_CYCLE_SECOND);
}

void FactoryMode_Exit(void)
{
    if(b_FctCtrlLoadOver == false)
    {
        Drive_ValveForce(false);
    }
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDefHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamperHeater, DS_DontCare);
    Stop_PollTimer();
}

void Get_FctUploadData(char *result, uint16_t len)
{
    str_n_cat(result, 1, factory_data);
}

uint16_t Get_FactoryRoomTemp(void)
{
    return (fct_room_temp);
}

bool Get_WifiFactoryModeResult(void)
{
    return b_WifiFactoryResponsed;
}

bool Get_WifiFactoryConnected(void)
{
    return b_WifiConnected;
}
