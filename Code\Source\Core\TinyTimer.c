/*!
 * @file
 * @brief
 *
 * Copyright xxxx - Confidential - All rights reserved.
 */

#include "TinyTimer.h"

static TinyTimerModule_t st_TinyTimerModule;

void TinyTimerModule_Init(I_TinyTimeSource_t *timeSource)
{
    st_TinyTimerModule._private.timeSource = timeSource;
    st_TinyTimerModule._private.lastTicks = TinyTimeSource_GetTicks(timeSource);

    TinyLinkedList_Init(&st_TinyTimerModule._private.timers);
}

static void StartTimer(TinyTimer_t *timer, TinyTimerTicks_t ticks, TinyTimerCallback_t callback, void *context)
{
    timer->_private.remainingTicks = ticks;
    timer->_private.startTicks = ticks;
    timer->_private.callback = callback;
    timer->_private.context = context;

    TinyLinkedList_Remove(&st_TinyTimerModule._private.timers, &timer->_private.node);
    TinyLinkedList_Insert(&st_TinyTimerModule._private.timers, &timer->_private.node);
}

void TinyTimerModule_StartOneShot(TinyTimer_t *timer, TinyTimerTicks_t ticks, TinyTimerCallback_t callback, void *context)
{
    timer->_private.autoReload = false;
    StartTimer(timer, ticks, callback, context);
}

void TinyTimerModule_StartPeriodic(TinyTimer_t *timer,
    TinyTimerTicks_t ticks,
    TinyTimerCallback_t callback,
    void *context)
{
    timer->_private.autoReload = true;
    StartTimer(timer, ticks, callback, context);
}

void TinyTimerModule_Stop(TinyTimer_t *timer)
{
    TinyLinkedList_Remove(&st_TinyTimerModule._private.timers, &timer->_private.node);
}

TinyTimerTicks_t TinyTimerModule_RemainingTicks(TinyTimer_t *timer)
{
    return timer->_private.remainingTicks;
}

bool TinyTimerModule_Run(void)
{
    TinyTimeSourceTickCount_t currentTicks = TinyTimeSource_GetTicks(st_TinyTimerModule._private.timeSource);
    TinyTimerTicks_t elapsedTicks = currentTicks - st_TinyTimerModule._private.lastTicks;
    st_TinyTimerModule._private.lastTicks = currentTicks;
    bool timerHasBeenCalledBack = false;

    TinyLinkedList_ForEach(&st_TinyTimerModule._private.timers, TinyTimer_t, timer, {
        if(timer->_private.remainingTicks > elapsedTicks)
        {
            timer->_private.remainingTicks -= elapsedTicks;
        }
        else
        {
            TinyTimerModule_Stop(timer);
            timer->_private.callback();
            timerHasBeenCalledBack = true;

            // Auto-reload for periodic timers
            if(timer->_private.autoReload)
            {
                timer->_private.remainingTicks = timer->_private.startTicks;
                TinyLinkedList_Insert(&st_TinyTimerModule._private.timers, &timer->_private.node);
            }
        }
    });

    return timerHasBeenCalledBack;
}
