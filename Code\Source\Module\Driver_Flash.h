/*!
 * @file
 * @brief This file defines public constants, types and functions for the fan drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __DRIVER_FLASH_H
#define __DRIVER_FLASH_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#define APP_HEADER_SIZE 16
#define APP_MAGIC_NUM 0x89765432
#define OTA_MAGIC ((uint32_t)0x89765432)
#define OTA_START_FLAG ((uint32_t)0x55555555)
#define OTA_TESTUART_FLAG ((uint32_t)0x5aa5a55a)
#define OTA_DONE_FLAG ((uint32_t)0xaaaaaaaa)
#define MCU_MAX_VERSION (9999)
#define SYSPARM_MAGIC ((uint32_t)0xDEADBEAF)
#define SYSPARM_VERSION ((uint32_t)1)
#define SYSPARM_CYCLES ((uint16_t)100)
#define FLASH_ERASE_STATE (0xFF)
#if FLASH_ERASE_STATE == 0xFF
#define PARM_EXPIRED ((uint32_t)0x0)
#define PARM_HISTORY ((uint32_t)0xFFFFFFFE)
#define PARM_NOTEXPIRED ((uint32_t)0xFFFFFFFF)
#define OTA_QUITE_FLAG ((uint32_t)0xFFFFFFFF)
#define OTA_NORMAL_FLAG ((uint32_t)0x0)
#else
#define PARM_EXPIRED ((uint32_t)0xFFFFFFFF)
#define PARM_HISTORY ((uint32_t)0x00000001)
#define PARM_NOTEXPIRED ((uint32_t)0x0)
#define OTA_QUITE_FLAG ((uint32_t)0x0)
#define OTA_NORMAL_FLAG ((uint32_t)0xFFFFFFFF)
#endif
#define PARM_CRC_LENGTH (2)
#define APP_PROMOTE_TIMER (30)

/* BootLoader flash相关宏定义 */
#define BOOT_SIZE (48 * FLASH_SECTOR_SIZE) // BootLoader flash尺寸
#define BOOT_PARA_ADDRESS (FLASH_BASE + BOOT_SIZE - 2 * FLASH_SECTOR_SIZE) // BootLoader para存储地址

/* APP flash相关宏定义 */
#define APP_ADDRESS (FLASH_BASE + BOOT_SIZE) // APP程序存储基地址
#define APP_SIZE (391 * FLASH_SECTOR_SIZE) // APP flash尺寸

#define PARAM_ADDRESS (APP_ADDRESS + APP_SIZE) // 配置存储基地址
#define PARAM_SIZE (8 * FLASH_SECTOR_SIZE) // 配置存储空间大小

#define FACTORY_ADDRESS (PARAM_ADDRESS + PARAM_SIZE) // 工厂SN存储基地址
#define FACTORY_SIZE (1 * FLASH_SECTOR_SIZE) // 工厂SN空间大小

#define EEPARAM_ADDRESS (FACTORY_ADDRESS + FACTORY_SIZE)
#define EEPARAM_SIZE (16 * FLASH_SECTOR_SIZE)

#define MAINTENANCE_SECTION_ADDRESS (EEPARAM_ADDRESS + EEPARAM_SIZE)
#define MAINTENANCE_SECTION_SIZE (2 * FLASH_SECTOR_SIZE)
#define MAINTENANCE_SECTION_NUM 8

#define PANIC_LOG_ADDRESS (MAINTENANCE_SECTION_ADDRESS + MAINTENANCE_SECTION_SIZE * MAINTENANCE_SECTION_NUM)
#define PANIC_LOG_SIZE (16 * FLASH_SECTOR_SIZE)

#define OTA_LOG_ADDRESS      (PANIC_LOG_ADDRESS + PANIC_LOG_SIZE)
#define OTA_LOG_SIZE         (16 * FLASH_SECTOR_SIZE)

#define APP_PROMOTE_ADDR (0x20007FE0)

#define PRODUCT_SN_MAGIC ((uint32_t)0xB66BB6B6)
#define PRODUCT_SN_SIZE 18
#define PRODUCT_YEAR_OFFSET 11
#define PRODUCT_MONTH_OFFSET 12
#define PRODUCT_DAY_OFFSET 13
#define PRODUCT_SEQ_THOUSAND_OFFSET 14
#define PRODUCT_SEQ_HUNDRED_OFFSET 15
#define PRODUCT_SEQ_TEN_OFFSET 16
#define PRODUCT_SEQ_ONE_OFFSET 17

#define PRODUCT_MODEL_OFFSET 6
#define PRODUCT_MODEL_BYTES 8
#define PRODUCT_MODEL_SIZE 32
#define PRODUCT_MODEL_PREFIX "midjd.fridge."
#define PRODUCT_FAKE_SN_FPART "47689/"
#define PRODUCT_FAKE_SN_BPART "32C0001"

typedef struct
{
    uint32_t ota_version;
    uint32_t ota_flag;
    uint32_t quite_flag;
    uint32_t ota_magic;
} ota_param_st;

#pragma pack(1)
typedef struct
{
    uint8_t inspection; // 商检标识
    uint8_t ref_temp; // 冷藏设定温度
    uint8_t frz_temp; // 冷冻设定温度
    uint8_t infant_mode; // 母婴模式
    uint8_t user_mode; // 工作模式
    uint8_t linyun_power; // 灵云节能
    uint8_t linyun_mute; // 灵云静音
    uint8_t icemaker_func; // 制冰机功能
    uint8_t peek_valley_power; // 峰谷用电功能
    uint8_t icemaker_reserve_0; // 制冰预约时长0
    uint8_t icemaker_reserve_1; // 制冰预约时长1
    uint8_t icemaker_reserve_2; // 制冰预约时长2
    uint8_t icemaker_reserve_3; // 制冰预约时长3
    uint8_t icemaker_volume; //制冰冰格大小
    uint8_t ref_disable; //冷藏关闭开关
    uint8_t smart_grid_deforst;
    uint8_t reserved[32];
} sys_param_st;
#pragma pack()

#pragma pack(1)
typedef struct
{
    uint32_t magic;
    uint32_t version;
    uint32_t expired;
} param_header_st;
#pragma pack()

typedef struct
{
    void *latest;
    void *update;
    void *defparam;
    void *history;
    uint16_t param_size;
    uint16_t blockno;
    uint16_t blocks;
    uint32_t start_addr;
    uint32_t magic;
    uint32_t version;
    uint16_t cycles_100ms;
    uint16_t count_100ms;
    bool dirty;
    bool is_default;
    bool has_history;
} param_manager_st;

typedef enum
{
    SYSPARAM_INSPECTION = 0u,
    SYSPARAM_REFTEMP,
    SYSPARAM_FRZTEMP,
    SYSPARAM_INFANT_MODE,
    SYSPARAM_USER_MODE,
    SYSPARAM_LINGYUN_POWER,
    SYSPARAM_LINGYUN_MUTE,
    SYSPARAM_ICEMAKER_FUNC,
    SYSPARAM_PEEK_VALLEY_POWER,
    SYSPARAM_ICEMAKER_RESERVE0,
    SYSPARAM_ICEMAKER_RESERVE1,
    SYSPARAM_ICEMAKER_RESERVE2,
    SYSPARAM_ICEMAKER_RESERVE3,
    SYSPARAM_ICEMAKER_VOLUME,
    SYSPARAM_REF_DISABLE,
    SYSPARAM_SMARTGRID_DEFORST,
    SYSPARAM_MAX
} sysparam_type_e;

typedef enum
{
    PARAMTYPE_SYSPARAM = 0u,
    PARAMTYPE_MAINTENANCE_SECTION1,
    PARAMTYPE_MAINTENANCE_SECTION2,
    PARAMTYPE_MAINTENANCE_SECTION3,
    PARAMTYPE_MAINTENANCE_SECTION4,
    PARAMTYPE_MAINTENANCE_SECTION5,
    PARAMTYPE_MAINTENANCE_SECTION6,
    PARAMTYPE_MAINTENANCE_SECTION7,
    PARAMTYPE_MAINTENANCE_SECTION8,
    PARAMTYPE_MAX
} param_type_e;

typedef struct
{
    uint32_t magic;
    uint32_t count;
    uint16_t crc16;
    uint32_t boot_version;
    uint32_t boot_crc;
    uint32_t app_version;
    uint32_t app_crc;
} app_promote_st;

#pragma pack(1)
typedef struct
{
    uint32_t magic;
    uint32_t version;
    uint32_t length;
    uint16_t crc16;
    uint8_t pad[2];
} app_imageheader_st;
#pragma pack()

#pragma pack(1)
typedef struct
{
    uint32_t magic;
    uint8_t data[PRODUCT_SN_SIZE];
    uint16_t crc16;
} product_sn_st;
#pragma pack()

typedef struct
{
    product_sn_st sn;
    bool exist;
    uint8_t user_model[PRODUCT_MODEL_SIZE];
    uint8_t model[PRODUCT_MODEL_BYTES];
} product_sn_manager_st;

void Init_Flash(void);
void SetOtaFlag(bool quite);
void ClearOtaParam(void);
uint32_t GetMcuVersion(void);
bool IsQuiteOtaBoot(void);
void UpdateSysParam(void);
int SetSysParam(sysparam_type_e type, uint8_t val);
int GetSysParam(sysparam_type_e type, uint8_t *val);
int GetSysParamFromFlash(sysparam_type_e type, uint8_t *val);
void RecoverySysParam(void);
void RecoveryUsrSysParam(void);
uint32_t GetAppPromoteCount(void);
void ClearAppPromoteCount(void);
void SaveOfflineLog(void *buf, uint32_t len);
uint32_t GetBootVersion(void);
uint32_t GetAppVersion(void);
uint32_t GetBootCrc(void);
uint32_t GetAppCrc(void);
void SetTestUartOtaFlag(void);
void ClearProductSn(void);
bool CheckSnFormat(uint8_t *sn, uint8_t size);
int8_t WriteProductSn(uint8_t *sn, uint8_t size, bool sync);
int8_t ReadProductSn(uint8_t *sn, uint8_t size);
int8_t ReadProductModel(uint8_t *model, uint8_t size);
int8_t ReadProductUserModel(uint8_t *user_model, uint8_t size);
void ReadEeParam(void *buf, uint32_t len);
void SaveEeParam(void *buf, uint8_t packno);
void Init_Maintenance(uint8_t *mdata);
int GetMaintenanceParam(uint8_t index, uint16_t *val);
int SetMaintenanceParam(uint8_t index, uint16_t *val);
int8_t ReadProductFakeSn(uint8_t *sn, uint8_t size);
int GetMaintenanceSectionParam(uint8_t section, uint8_t offset, uint16_t *val);
#endif
