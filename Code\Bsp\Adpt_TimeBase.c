/*!
 * @file
 * @brief Configures the SysTick.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "sysctrl.h"
#include "bt.h"
#include "gpio.h"
#include "Timebase.h"

// TIME0:  500us 定时中断
// 系统主频SystemCoreClock,time设置8分频
#define TIME0_PERIOD_VALUE ((uint16_t)((SystemCoreClock / (8 * 1000000)) * 500))

void Time0Init(void)
{
    uint16_t u16ArrValue;
    uint16_t u16CntValue;
    stc_bt_mode0_cfg_t stcBtBaseCfg;

    DDL_ZERO_STRUCT(stcBtBaseCfg);

    Sysctrl_SetPeripheralGate(SysctrlPeripheralBaseTim, TRUE); /* Base Timer外设时钟使能 */

    stcBtBaseCfg.enWorkMode = BtWorkMode0; // 定时器模式
    stcBtBaseCfg.enCT = BtTimer; // 定时器功能，计数时钟为内部PCLK
    stcBtBaseCfg.enPRS = BtPCLKDiv8; // PCLK/8
    stcBtBaseCfg.enCntMode = Bt16bitArrMode; // 自动重载16位计数器/定时器
    stcBtBaseCfg.bEnTog = FALSE;
    stcBtBaseCfg.bEnGate = FALSE;
    stcBtBaseCfg.enGateP = BtGatePositive;
    Bt_Mode0_Init(TIM0, &stcBtBaseCfg); // TIM 的模式0功能初始化

    u16ArrValue = (uint16_t)0x10000 - TIME0_PERIOD_VALUE;
    Bt_M0_ARRSet(TIM0, u16ArrValue); // 设置重载值(ARR = 0x10000 - 周期)

    u16CntValue = (uint16_t)0x10000 - TIME0_PERIOD_VALUE;
    Bt_M0_Cnt16Set(TIM0, u16CntValue); // 设置计数初值

    Bt_ClearIntFlag(TIM0, BtUevIrq); // 清中断标志
    Bt_Mode0_EnableIrq(TIM0); // 使能TIM中断(模式0时只有一个中断)
    EnableNvic(TIM0_IRQn, IrqLevel2, TRUE); // TIM中断使能

    Bt_M0_Run(TIM0); // TIM 运行。
}

void Tim0_IRQHandler(void)
{
    // Timer1 模式0 溢出中断
    if(TRUE == Bt_GetIntFlag(TIM0, BtUevIrq))
    {
        ISR_Timer_500us();
        Bt_ClearIntFlag(TIM0, BtUevIrq); // 清中断标志
    }
}

/**
 * @brief  This function handles SysTick Handler.
 */
void SysTick_IRQHandler(void)
{
    __disable_irq();
    ISR_Timer_1ms();
    __enable_irq();
}

void Board_InitSysTick(void)
{
    /* Setup SysTick Timer for 1 msec interrupts  */
    /* 内核函数，SysTick配置，定时1ms，系统时钟默认RCH 4MHz */
    if(SysTick_Config((SystemCoreClock) / 1000))
    {
        /* Capture error */
        while(1)
            ;
    }

    Time0Init();
}
