/*!
 * @file
 * @brief Manages all the state variables of the self-checking.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "FunctionalCircuitTest.h"
#include "Core_CallBackTimer.h"
#include "ResolverDevice.h"
#include "Driver_GradualLamp.h"
#include "IO_Device.h"
#include "Driver_SingleDamper.h"
#include "Driver_CompFrequency.h"
#include "SystemManager.h"
#include "Driver_Fan.h"
#include "DisplayInterface.h"
#include "Driver_Flash.h"
#include "Drive_Valve.h"
#include "Sbus_IceMaker.h"

#define U16_FCT_MODE_CYCLE_SECOND (uint16_t)1
#define U16_FCT_MODE_EXIT_SECOND (uint16_t)30

static st_CoreCallbackTimer st_FctModeTimer;
static uint16_t u16_FctControlCount;
static uint8_t u8_Device_ID;
static uint8_t u8_Value;

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_FctModeTimer);
}

static void Close_AllLoad(void)
{
    uint8_t icemaker_load = 0xFF;

    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, FREQ_0HZ); // 压缩机
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_Off); // 冷冻风机
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_Off); // 冷却风机
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_Off); // 主风门 冷藏风门
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DS_Off); // 从风门 变温风门
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off); // 纵梁加热丝
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off); // 冷冻化霜加热丝
    Test_GradualLamp(REF_SURFACE_LAMP, false); // 冷藏
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzLeftLamp, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzRightLamp, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, MAX_ValveState - 1);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_IonGenerator, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamperHeater, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzIonGenerator, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDefHeater, DS_Off);
    SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
}

static void Exit_ControlLoad(void)
{
    Close_AllLoad();
    RecoverySysParam();
    FridgeState_Update((FridgeState_t)eFridge_Running);
}

static void FunctionalCircuitTest_ControlLoad(void)
{
    uint8_t icemaker_load;

    switch(u8_Device_ID)
    {
        case DEVICE_All_Check:
            if(u8_Value == 1)
            {
                Close_AllLoad();
            }
            break;
        case DEVICE_Comp_Check:
            if(u8_Value < FREQ_MAX)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, u8_Value); // 压缩机
            }
            break;
        case DEVICE_FrzFan_Check:
            if(u8_Value < 100)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, u8_Value); // 冷冻风机
            }
            break;
        case DEVICE_CoolFan_Check:
            if(u8_Value < 100)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, u8_Value); // 冷却风机
            }
            break;
        case DEVICE_DoubleDamper_Check:
            if(u8_Value > 0)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_On); // 主风门 冷藏风门
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DS_On); // 从风门 变温风门
            }
            else
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DAMPER_AllClose); // 主风门 冷藏风门
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DAMPER_AllClose); // 从风门 变温风门
            }
            break;
        case DEVICE_VerticalBeamHeater_Check:
            if(u8_Value > 0)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_On); // 纵梁加热丝
            }
            else
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off); // 纵梁加热丝
            }
            break;
        case DEVICE_FrzDefHeater_Check:
            if(u8_Value > 0)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_On); // 冷冻化霜加热丝
            }
            else
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off); // 冷冻化霜加热丝
            }
            break;
        case DEVICE_RefLamp_Check: // 冷藏
            if(u8_Value == 1)
            {
                Test_GradualLamp(REF_SURFACE_LAMP, true);
            }
            else if(u8_Value == 0)
            {
                Test_GradualLamp(REF_SURFACE_LAMP, false);
            }
            break;
        case DEVICE_FrzLamp_Check:
            if(u8_Value == 1)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzLeftLamp, DS_On);
            }
            else if(u8_Value == 0)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzLeftLamp, DS_Off);
            }
            break;
        case DEVICE_RefVarLamp_Check:
            if(u8_Value == 1)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzRightLamp, DS_On);
            }
            else if(u8_Value == 0)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzRightLamp, DS_Off);
            }
            break;
        case DEVICE_RefFan_Check:
            if(u8_Value < 100)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, u8_Value);
            }
            break;
        case DEVICE_Valve_Check:
            if(u8_Value < MAX_ValveState)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, u8_Value);
            }
            break;
        case DEVICE_IonGenerator_Check:
            if(u8_Value == 1)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_IonGenerator, DS_On);
            }
            else if(u8_Value == 0)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_IonGenerator, DS_Off);
            }
            break;
        case DEVICE_VarDamperHearter_Check:
            if(u8_Value == 1)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamperHeater, DS_On);
            }
            else if(u8_Value == 0)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamperHeater, DS_Off);
            }
            break;
        case DEVICE_FrzIonGenerator_Check:
            if(u8_Value == 1)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzIonGenerator, DS_On);
            }
            else if(u8_Value == 0)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzIonGenerator, DS_Off);
            }
            break;
        case DEVICE_RefDefHeater_Check:
            if(u8_Value > 0)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDefHeater, DS_On);
            }
            else
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDefHeater, DS_Off);
            }
            break;
        case DEVICE_RefSingleDamper_Check:
            if(u8_Value > 0)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_On);
            }
            else
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DAMPER_AllClose);
            }
            break;
        case DEVICE_VarSingleDamper_Check:
            if(u8_Value > 0)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DS_On);
            }
            else
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DAMPER_AllClose);
            }
            break;
        case DEVICE_IceMakerWaterPump_Check:
            if(u8_Value > 0)
            {
                icemaker_load = 0x3;
                SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
            }
            else
            {
                icemaker_load = 0xFF;
                SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
            }
            break;
        case DEVICE_IceMakerFlip_Check:
            if(u8_Value > 0)
            {
                icemaker_load = 0x2;
                SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
            }
            else
            {
                icemaker_load = 0xFF;
                SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
            }
            break;
        case DEVICE_IceMakerWaterHeater_Check:
            if(u8_Value > 0)
            {
                icemaker_load = 0x1;
                SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
            }
            else
            {
                icemaker_load = 0xFF;
                SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
            }
            break;

            break;
        case DEVICE_Exit_Check:
            if(u8_Value == 1)
            {
                Exit_ControlLoad();
            }
            break;
        default:
            break;
    }
    u16_FctControlCount++;
    if(u16_FctControlCount >= U16_FCT_MODE_EXIT_SECOND)
    {
        Exit_ControlLoad();
    }
}

static void Start_PollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_FctModeTimer,
        FunctionalCircuitTest_ControlLoad,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

void FunctionalCircuitTest_Init(void)
{
    if(IsValveAlreadyReset() == false)
    {
        Drive_ValveReset();
    }

    if(IsSingleDamperAlreadyReset(Damper_Var) == false)
    {
        Reset_SingleDamper(Damper_Var);
    }
    Start_PollTimer(U16_FCT_MODE_CYCLE_SECOND);
}

void FunctionalCircuitTest_Exit(void)
{
    uint8_t icemaker_load = 0;

    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, DS_DontCare); // 压缩机
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare); // 冷冻风机
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare); // 冷却风机
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_DontCare); // 主风门 冷藏风门
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DS_DontCare); // 从风门 变温风门
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_DontCare); // 纵梁加热丝
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_DontCare); // 冷冻化霜加热丝
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzLeftLamp, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzRightLamp, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_IonGenerator, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamperHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzIonGenerator, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDefHeater, DS_DontCare);
    SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD, &icemaker_load);
    Stop_PollTimer();
}

void Set_SelfCheckDeviceIDAndValue(uint8_t entryNumber, uint8_t entryValue)
{
    u8_Device_ID = entryNumber;
    u8_Value = entryValue;
    u16_FctControlCount = 0;
}