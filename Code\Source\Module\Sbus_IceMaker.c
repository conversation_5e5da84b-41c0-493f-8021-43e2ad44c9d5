/*!
 * @file
 * @brief Manages all the state variables of the cooling controller.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "Crc16_CCITT_FALSE.h"
#include "Parameter_TemperatureZone.h"
#include "DisplayInterface.h"
#include "SystemTimerModule.h"
#include "Driver_DoorSwitch.h"
#include "ResolverDevice.h"
#include "ParameterManager.h"
#include "SystemManager.h"
#include "CoolingCycle.h"
#include "FaultCode.h"
#include "Driver_Flash.h"
#include "Sbus_Display.h"
#include "Sbus_IceMaker.h"
#include "Sbus_Nfc.h"
#include "CloudControl.h"
#include "FridgeRunner.h"
#include "Defrosting.h"
#include "syslog.h"
#include "Driver_Emulator.h"
icemaker_param_st icemaker_param = {
    .mtype = MACHINE_TYPE_UNKOWN,
    .icemaker_mode = eIceMaker_Stop_Mode,
    .init = 1,
};

sbus_icemaker_st icemaker;

static void sync_poweron(property_reponse_e result)
{
    if(PROPERTY_RESPONSE_SUCCESS == result)
    {
        icemaker.init = true;
        icemaker_param.init = 0;
    }
}

static void sync_icemaker(void *data)
{
    uint8_t mode = *(uint8_t *)data;
    if(icemaker_param.init == 1)
    {
        return;
    }
    SetSysParam(SYSPARAM_ICEMAKER_FUNC, mode);
}

static bool update_icemaker_mode(void)
{
    uint8_t ice_maker;

    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &ice_maker);
    if((ice_maker == eIceMaker_Quick_Mode) &&
       (true == Get_Strong_Mute()))
    {
        ice_maker = eIceMaker_Normal_Mode;
    }

    if(icemaker_param.icemaker_mode != ice_maker)
    {
        icemaker_param.icemaker_mode = ice_maker;
        return true;
    }
    return false;
}

static bool update_icemaker_volume(void)
{
    uint8_t vol;

    GetSysParam(SYSPARAM_ICEMAKER_VOLUME, &vol);
    if(icemaker_param.icemaker_volume != vol)
    {
        icemaker_param.icemaker_volume = vol;
        return true;
    }
    return false;
}

static bool update_frz_temp(void)
{
    uint16_t temp;

    temp = Get_SensorValue(SENSOR_FRZ);
    if(icemaker_param.frz_temp != temp)
    {
        icemaker_param.frz_temp = temp;
        return true;
    }
    return false;
}

static bool update_room_temp(void)
{
    uint8_t temp;

    temp = Get_RoomTempRange();
    if(icemaker_param.room_temp != temp)
    {
        icemaker_param.room_temp = temp;
        return true;
    }
    return false;
}

static void sync_icemaker_test(void *data)
{
    if(icemaker_param.init == 1)
    {
        return;
    }

    SetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_ICEMAKER_TEST, data);
}

static void sync_icemaker_clean(void *data)
{
    if(icemaker_param.init == 1)
    {
        return;
    }

    SetNfcPropertyValue(NFC_PROPERTY_TYPE_CLEAN_STATE, data);
}

static void sync_icemaker_load(void *data)
{
    uint8_t temp = *(uint8_t *)data;

    if(icemaker_param.init == 1)
    {
        return;
    }

    if(temp & (1 << 0))
    {
        icemaker.water_timer = ICEMAKER_WATER_TIMER_MS;
        Vote_DeviceStatus((uint8_t)FSM_IceMakerControl, DEVICE_FrzFan, DS_Off);
    }
}

static bool update_mode(void)
{
    uint8_t mode = Get_UserMode();

    if(icemaker_param.mode != mode)
    {
        icemaker_param.mode = mode;
        return true;
    }
    return false;
}

static bool update_frz_set(void)
{
    int8_t frzSet;
    uint8_t val;

    //GetSysParam(SYSPARAM_FRZTEMP, &val);
    val = Get_FrzSetTemp();
    frzSet = val - 30;

    if(icemaker_param.frz_set_temp != frzSet)
    {
        icemaker_param.frz_set_temp = frzSet;
        return true;
    }
    return false;
}

static bool update_door_state(void)
{
    uint16_t state = 0;

    if(Get_DoorSwitchState(DOOR_REF_LEFT) == true)
    {
        state |= 1;
    }

    if(Get_DoorSwitchState(DOOR_REF_RIGHT) == true)
    {
        state |= 2;
    }

    if(Get_DoorSwitchState(DOOR_FRZ_LEFT) == true)
    {
        state |= 4;
        state |= 0x40;
    }

    if(Get_DoorSwitchState(DOOR_FRZ_RIGHT) == true)
    {
        state |= 8;
    }

    if(icemaker_param.door_state != state)
    {
        icemaker_param.door_state = state;
        return true;
    }
    return false;
}

static bool update_deforst_state(void)
{
    uint8_t dstate;
    RunningState_t state = Get_RunningState();

    dstate = (state == eRunning_Defrosting) ? 1 : 0;
    if(icemaker_param.deforst_state != dstate)
    {
        icemaker_param.deforst_state = dstate;
        return true;
    }
    return false;
}

static bool update_deforst_sensor(void)
{
    int8_t temp;

    if(icemaker_param.deforst_state == 0)
    {
        return false;
    }

    temp = (Get_SensorValue(SENSOR_DEFROST) - 500) / 10;
    if(icemaker_param.deforst_sensor != temp)
    {
        icemaker_param.deforst_sensor = temp;
        return true;
    }
    return false;
}

static bool update_system_state(void)
{
    uint8_t state;

    state = Get_FridgeState();
    if(icemaker_param.system_state != state)
    {
        icemaker_param.system_state = state;
        return true;
    }
    return false;
}

static bool update_deforst_mode(void)
{
    uint8_t mode;

    mode = Get_DefrostMode();

    if(icemaker_param.deforst_mode != mode)
    {
        icemaker_param.deforst_mode = mode;
        return true;
    }
    return false;
}

static bool update_cooling_state(void)
{
    uint8_t state;

    state = Get_CoolingCapacityState();
    if(icemaker_param.cooling_state != state)
    {
        icemaker_param.cooling_state = state;
        return true;
    }
    return false;
}

static bool update_machine_type(void)
{
    uint8_t mt = GetMachineType();

    if(mt == MACHINE_TYPE_UNKOWN)
    {
        return false;
    }

    if(icemaker_param.mtype != mt)
    {
        icemaker.mtype_timer = Get_MinuteCount();
        icemaker_param.mtype = mt;
        return true;
    }

    if(Get_MinuteElapsedTime(icemaker.mtype_timer) > ICEMAKER_MTYPE_SYNC_MINUTES)
    {
        icemaker.mtype_timer = Get_MinuteCount();
        return true;
    }
    return false;
}

static bool update_pfault_state(void)
{
    uint32_t pfault = 0;
    bool err_state = Get_SensorError((uint8_t)SENSOR_HUMIDITY);

    if(err_state)
    {
        pfault |= 1 << 0;
    }

    if(icemaker_param.pfault != pfault)
    {
        icemaker_param.pfault = pfault;
        return true;
    }
    return false;
}

static bool update_fault_code(void)
{
    uint32_t error = 0;
    uint32_t error0 = Get_FaultCodeByte(eFCode_IotByte0);
    uint32_t error1 = Get_FaultCodeByte(eFCode_IotByte1);
    uint32_t error2 = Get_FaultCodeByte(eFCode_IotByte2);
    uint32_t error3 = Get_FaultCodeByte(eFCode_IotByte3);

    error = error0 + (error1 << 8) + (error2 << 16) + (error3 << 24);
    if(icemaker_param.error != error)
    {
        icemaker_param.error = error;
        return true;
    }
    return false;
}

fireware_property_st icemaker_propertys[ICEMAKER_PROPERTY_TYPE_MAX] = {
    { PROPERTY_VAL_UINT8, 250, 250, &icemaker_param.mtype, PROPERTY_DIR_OUT | PROPERTY_ALLDIRITY_FILTER, false, NULL, NULL, update_machine_type, false, 0 },
    { PROPERTY_VAL_UINT16, 20, 1, &icemaker_param.temp_bottom, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT16, 20, 2, &icemaker_param.temp_top, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 20, 3, &icemaker_param.ledctrl, PROPERTY_DIR_OUT, false, NULL, NULL, NULL },
    { PROPERTY_VAL_UINT16, 20, 9, &icemaker_param.temp_bottomx, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT16, 20, 5, &icemaker_param.frz_temp, PROPERTY_DIR_OUT, false, NULL, NULL, update_frz_temp, false, 0 },
    { PROPERTY_VAL_UINT8, 20, 6, &icemaker_param.room_temp, PROPERTY_DIR_OUT, false, NULL, NULL, update_room_temp, false, 0 },
    { PROPERTY_VAL_UINT8, 2, 2, &icemaker_param.mode, PROPERTY_DIR_OUT, false, NULL, NULL, update_mode, false, 0 },
    { PROPERTY_VAL_INT8, 4, 2, &icemaker_param.frz_set_temp, PROPERTY_DIR_OUT, false, NULL, NULL, update_frz_set, false, 0 },
    { PROPERTY_VAL_UINT8, 21, 2, &icemaker_param.icemaker_test, PROPERTY_DIR_INOUT, false, sync_icemaker_test, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 21, 3, &icemaker_param.clean_state, PROPERTY_DIR_IN, false, sync_icemaker_clean, NULL, NULL },
    { PROPERTY_VAL_UINT8, 21, 5, &icemaker_param.icemaker_force, PROPERTY_DIR_OUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 21, 4, &icemaker_param.icemaker_fault, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT16, 21, 6, &icemaker_param.force_icemaker_time, PROPERTY_DIR_OUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 21, 7, &icemaker_param.force_wheater_time, PROPERTY_DIR_OUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 21, 8, &icemaker_param.wheater_time, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT16, 21, 9, &icemaker_param.force_waterlog_time, PROPERTY_DIR_OUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT16, 21, 10, &icemaker_param.icemaker_waterlog, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 21, 13, &icemaker_param.force_bheater_time, PROPERTY_DIR_OUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 21, 14, &icemaker_param.bheater_time, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT16, 21, 15, &icemaker_param.force_isensor_onoff_val, PROPERTY_DIR_OUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT16, 21, 16, &icemaker_param.isensor_val, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 1, &icemaker_param.deforst_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_deforst_state, false, 0 },
    { PROPERTY_VAL_INT8, 10, 2, &icemaker_param.deforst_sensor, PROPERTY_DIR_OUT, false, NULL, NULL, update_deforst_sensor, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 13, &icemaker_param.system_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_system_state, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 15, &icemaker_param.deforst_mode, PROPERTY_DIR_OUT, false, NULL, NULL, update_deforst_mode, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 17, &icemaker_param.cooling_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_cooling_state, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 31, &icemaker_param.icemaker_load, PROPERTY_DIR_IN, false, sync_icemaker_load, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 32, &icemaker_param.icemaker_doing, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT16, 10, 33, &icemaker_param.icemaker_target, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT16, 10, 34, &icemaker_param.icemaker_history, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT16, 10, 35, &icemaker_param.icemaker_current, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT32, 2, 1, &icemaker_param.error, PROPERTY_DIR_OUT, false, NULL, NULL, update_fault_code, false, 0 },
    { PROPERTY_VAL_UINT32, 10, 36, &icemaker_param.pfault, PROPERTY_DIR_OUT, false, NULL, NULL, update_pfault_state, false, 0 },
    { PROPERTY_VAL_UINT8, 8, 1, &icemaker_param.icemaker_mode, PROPERTY_DIR_INOUT, false, sync_icemaker, NULL, update_icemaker_mode, false, 0 },
    { PROPERTY_VAL_UINT8, 8, 3, &icemaker_param.icemaker_state, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT32, 8, 4, &icemaker_param.icemaker_reserve, PROPERTY_DIR_INOUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 8, 5, &icemaker_param.icemaker_volume, PROPERTY_DIR_OUT, false, NULL, NULL, update_icemaker_volume, false, 0 },
    { PROPERTY_VAL_UINT8, 8, 6, &icemaker_param.reserve_state, PROPERTY_DIR_INOUT, false, NULL, NULL, NULL },
    { PROPERTY_VAL_UINT16, 10, 11, &icemaker_param.door_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_door_state, false, 0 },
    { PROPERTY_VAL_UINT16, 20, 10, &icemaker_param.short_time, PROPERTY_DIR_OUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 7, 2, &icemaker_param.init, PROPERTY_DIR_INOUT, false, NULL, sync_poweron, NULL, false, 0 },
};

static int32_t IceMaker_recvframe(fireware_frame_st *data)
{
    uint8_t *rdata;
    uint16_t rlen = 0;
    fireware_property_st *prop;
    uint8_t index;

    if(icemaker.poll_count == 0)
    {
        icemaker.poll_count = ICEMAKER_EVENT_POLL_TIMER_MS;
        if(Is_Sbus_Slave_Err(&icemaker.slave) == true)
        {
            icemaker.poll_count = ICEMAKER_ERROR_EVENT_POLL_TIMER_MS;
        }
    }

    if(NULL == data)
    {
        if(icemaker.dirty && icemaker.frame.fb1 == FIREWARE_PROPERTY_FUNC && icemaker.frame.fb2 == PROPERTY_SUBFUNC_IOT)
        {
            icemaker.dirty = false;
            if(icemaker.init == false)
            {
                icemaker.b_appversion = false;
                icemaker.alldirty = true;
            }
        }
        return -1;
    }
    else
    {
        if(data->srcAddr != FIREWARE_ICEMAKER_ADDR)
        {
            err("(%d, %d)not refvar frame\n", data->srcAddr, data->fb1);
            return -1;
        }

        if(data->fb1 == FIREWARE_PROPERTY_FUNC)
        {
            if(data->fb2 == PROPERTY_SUBFUNC_QUERY_RESPONSE)
            {
                if(data->len > 0)
                {
                    rdata = Parse_PropertyData(data->data, data->len, &rlen, icemaker_propertys, ICEMAKER_PROPERTY_TYPE_MAX);
                    Edit_Property_ResponseFrame(FIREWARE_ICEMAKER_ADDR, &icemaker.frame, rdata, rlen);
                    Sbus_Slave_Request(&icemaker.slave, SBUS_PKT_PRI_LEVEL7);
                    icemaker.response = true;
                    icemaker.dirty = false;
                }
                if(icemaker_param.init == 1)
                {
                    icemaker.b_bootversion = false;
                    icemaker.b_appversion = false;
                    icemaker_param.init = 0;
                    icemaker_param.mtype = MACHINE_TYPE_UNKOWN;
                    icemaker.init = false;
                    icemaker.dirty = false;
                    icemaker.alldirty = true;
                }
            }
            else if(data->fb2 == PROPERTY_SUBFUNC_RESPONSE)
            {
                if(data->len > 0)
                {
                    Parse_PropertyResponse(data->data, data->len, icemaker_propertys, ICEMAKER_PROPERTY_TYPE_MAX);
                }
                icemaker.dirty = false;
            }
        }
        else if(data->fb1 == FIREWARE_OTA_FUNC)
        {
            if(data->fb2 == FIREWARE_OTA_QUERY_VER)
            {
                icemaker.hwversion = data->data[0] << 8 | data->data[1];
                icemaker.appversion = data->data[2] << 8 | data->data[3];
                icemaker.appcrc = data->data[4] << 8 | data->data[5];
                icemaker.b_appversion = true;
            }

            if(data->fb2 == FIREWARE_OTA_BOOT_VER)
            {
                icemaker.bootVersion = data->data[0] << 8 | data->data[1];
                icemaker.bootCrc = data->data[2] << 8 | data->data[3];
                icemaker.b_bootversion = true;
            }
        }
    }
    return 0;
}

static fireware_frame_st *IceMaker_sendframe(sbus_pkt_pri_e pri)
{
    if(pri == SBUS_PKT_PRI_LEVEL6)
    {
        if(icemaker.b_appversion == false)
        {
            Edit_OtaControlFrame(&icemaker.frame,
                icemaker.slave.addr,
                FIREWARE_OTA_QUERY_VER);
        }
        else
        {
            if(icemaker.init)
            {
                Edit_Property_QueryFrame(icemaker.slave.addr, &icemaker.frame);
            }
            else
            {
                return NULL;
            }
        }
    }
    else if(pri == SBUS_PKT_PRI_LEVEL5)
    {
        if(icemaker.dirty == true)
        {
            Edit_Property_Frame(icemaker.slave.addr, &icemaker.frame, icemaker_propertys, ICEMAKER_PROPERTY_TYPE_MAX);
        }
        else
        {
            return NULL;
        }
    }
    else if(pri == SBUS_PKT_PRI_LEVEL4)
    {
        if(icemaker.b_bootversion == false)
        {
            Edit_OtaControlFrame(&icemaker.frame,
                icemaker.slave.addr,
                FIREWARE_OTA_BOOT_VER);
        }
        else
        {
            return NULL;
        }
    }
    else if(pri == SBUS_PKT_PRI_LEVEL7)
    {
        if(icemaker.response == true)
        {
            icemaker.response = false;
        }
        else
        {
            return NULL;
        }
    }

    return &icemaker.frame;
}

void Init_SbusIceMaker(void)
{
    uint8_t index;
    fireware_property_st *prop;

    icemaker.slave.addr = FIREWARE_ICEMAKER_ADDR;
    icemaker.slave.ops.recvFrame = IceMaker_recvframe;
    icemaker.slave.ops.sendFrame = IceMaker_sendframe;
    icemaker.poll_count = ICEMAKER_EVENT_POLL_TIMER_MS;
    icemaker.boot_count = 0;
    icemaker.init = false;
    icemaker_propertys[ICEMAKER_PROPERTY_TYPE_INIT].dirty = true;
    icemaker.dirty = false;
    icemaker.alldirty = true;
    icemaker.mtype_timer = Get_MinuteCount();
    Sbus_Slave_Register(SBUS_TYPE_ID0, &icemaker.slave);
}

void Update_IceMaker_Param(void)
{
    bool update;
    uint8_t index;
    fireware_property_st *prop;

    if(icemaker.dirty)
    {
        return;
    }

    for(index = 0; index < ICEMAKER_PROPERTY_TYPE_MAX; index++)
    {
        prop = &icemaker_propertys[index];
        if((prop->flags & PROPERTY_DIR_OUT) && prop->dirty1 == true)
        {
            prop->dirty1 = false;
            prop->dirty = true;
        }
        if((prop->flags & PROPERTY_DIR_OUT) &&
            prop->update_property)
        {
            update = prop->update_property();
            if(update == true)
            {
                prop->dirty = true;
            }
        }

        if((prop->flags & PROPERTY_DIR_OUT) &&
            (prop->dirty == true || icemaker.alldirty == true))
        {
            prop->dirty = true;
            icemaker.dirty = true;
        }
    }

    if(icemaker.alldirty == true)
    {
        icemaker.alldirty = false;
    }

    if(icemaker.dirty)
    {
        Sbus_Slave_Request(&icemaker.slave, SBUS_PKT_PRI_LEVEL5);
    }
}

void Handle_IceMaker_Overtime(void)
{
    if(icemaker.poll_count > 0)
    {
        icemaker.poll_count--;
        if(icemaker.poll_count == 0)
        {
            Sbus_Slave_Request(&icemaker.slave, SBUS_PKT_PRI_LEVEL6);
            if(Is_Sbus_Slave_Err(&icemaker.slave) == true)
            {
                Update_IceMaker_Param();
            }
        }
    }

    if(Is_Sbus_Slave_Err(&icemaker.slave) == false)
    {
        Update_IceMaker_Param();
    }

    if(icemaker.water_timer > 0)
    {
        icemaker.water_timer--;
        if(icemaker.water_timer == 0)
        {
            Vote_DeviceStatus((uint8_t)FSM_IceMakerControl, DEVICE_FrzFan, DS_DontCare);
        }
    }

    if(icemaker.b_bootversion == false && icemaker.boot_count++ >= ICEMAKER_BOOT_MS)
    {
        icemaker.boot_count = 0;
        Sbus_Slave_Request(&icemaker.slave, SBUS_PKT_PRI_LEVEL4);
    }
}

int8_t GetIceMakerPropertyValue(icemaker_property_type_e type, void *data)
{
    if(type >= ICEMAKER_PROPERTY_TYPE_MAX || data == NULL)
    {
        return FIREWARE_ERROR;
    }
    memcpy(data, icemaker_propertys[type].data, get_Property_Size(icemaker_propertys[type].val));
    return FIREWARE_SUCCESS;
}

int8_t SetIceMakerPropertyValue(icemaker_property_type_e type, void *data)
{
    if(type >= ICEMAKER_PROPERTY_TYPE_MAX || data == NULL)
    {
        return FIREWARE_ERROR;
    }
    memcpy(icemaker_propertys[type].data, data, get_Property_Size(icemaker_propertys[type].val));
    if(icemaker.dirty == true)
    {
        icemaker_propertys[type].dirty1 = true;
    }
    else
    {
        icemaker_propertys[type].dirty = true;
    }

    return FIREWARE_SUCCESS;
}

bool GetIceMakerWorkErr(void)
{
    if(icemaker_param.icemaker_fault & (1 << 1))
    {
        return true;
    }
    return false;
}

bool GetIceMakerDownSensorErr(void)
{
    if(icemaker_param.icemaker_fault & (1 << 2))
    {
        return true;
    }
    return false;
}

bool GetIceMakerUpSensorErr(void)
{
    if(icemaker_param.icemaker_fault & (1 << 3))
    {
        return true;
    }
    return false;
}

bool GetIceMakerDownBehindSensorErr(void)
{
    if(icemaker_param.icemaker_fault & (1 << 4))
    {
        return true;
    }
    return false;
}

bool GetIceMakerPropertyState(icemaker_property_type_e type)
{
    if(type >= ICEMAKER_PROPERTY_TYPE_MAX)
    {
        return false;
    }

    return (icemaker_propertys[type].dirty || icemaker_propertys[type].dirty1);
}

__EMULATOR__FUNCITON bool Get_IceMakerCommErr(void)
{
    return (Is_Sbus_Master_Err(&icemaker.slave) || Is_Sbus_Slave_Err(&icemaker.slave));
}

uint32_t Get_IceMakerBootVersion(void)
{
    if(icemaker.b_bootversion)
    {
        return icemaker.bootVersion;
    }

    return 0;
}

uint32_t Get_IceMakerBootCrc(void)
{
    if(icemaker.b_bootversion)
    {
        return icemaker.bootCrc;
    }

    return 0;
}

uint32_t Get_IceMakerAppVersion(void)
{
    if(icemaker.b_appversion)
    {
        return icemaker.appversion;
    }

    return 0;
}

uint32_t Get_IceMakerAppCrc(void)
{
    if(icemaker.b_appversion)
    {
        return icemaker.appcrc;
    }

    return 0;
}

void Ctrl_LeftVarLed(bool enable)
{
    uint8_t ctrl = 0;

    ctrl = icemaker_param.ledctrl;
    if(enable)
    {
        ctrl |= 1 << 0;
    }
    else
    {
        ctrl &= ~(1 << 0);
    }

    if(ctrl != icemaker_param.ledctrl)
    {
        SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_LEDCTRL, &ctrl);
    }
    return;
}

void Ctrl_RightVarLed(bool enable)
{
    uint8_t ctrl = 0;

    ctrl = icemaker_param.ledctrl;
    if(enable)
    {
        ctrl |= 1 << 1;
    }
    else
    {
        ctrl &= ~(1 << 1);
    }

    if(ctrl != icemaker_param.ledctrl)
    {
        SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_LEDCTRL, &ctrl);
    }
    return;
}

sbus_slave_stats *Get_IceMakerPacketStats(void)
{
    return &icemaker.slave.stats;
}

