/*!
 * @file
 * @brief This file defines public constants, types and functions for the Core scheduler.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Core_Scheduler.h"
#include "TinyTimer.h"
#include "Core_TimeBase.h"
#include "Adpt_Iwdg.h"
#include "CoreUser_Scheduler_Table.h"

#define CORE_SCHEDULER_NUMBER_SCHEDULED_FUNCTIONS \
    (sizeof(CoreUser_Scheduler_aScheduleTable) / sizeof(st_CoreSchedulerItem))

/*!
 * @brief This type holds all local variables for this module.
 * @param TinyTimer_t  ast_SchedulerItemTimer[CORE_SCHEDULER_NUMBER_SCHEDULED_FUNCTIONS] :
 * The following array allocates a timer object for each scheduled item.
 * @param uint16_t u16_Seconds : The number of seconds
 */
typedef struct
{
    TinyTimer_t ast_SchedulerItemTimer[CORE_SCHEDULER_NUMBER_SCHEDULED_FUNCTIONS];
} st_CoreSchedulerStatus;

static st_CoreSchedulerStatus st_Status;

static TinyTimeSourceTickCount_t SysTick_GetTicks(I_TinyTimeSource_t *instance)
{
    uint16_t systickCounter = Core_TimeBase_GetSystemTickCounter();
    return (TinyTimeSourceTickCount_t)systickCounter;
}

static const I_TinyTimeSource_Api_t sysTickApi = {
    .GetTicks = SysTick_GetTicks
};

static I_TinyTimeSource_t sysTickTimeSource = {
    .api = &sysTickApi
};

/*!
 * @brief Initializes the Core scheduler module.
 * This function initializes the TinyTimer module and starts all scheduled functions with their respective intervals.
 */

void Core_Scheduler_Init(void)
{
    uint8_t u8_Index;
    TinyTimerModule_Init(&sysTickTimeSource);
    for(u8_Index = 0; u8_Index < CORE_SCHEDULER_NUMBER_SCHEDULED_FUNCTIONS; u8_Index++)
    {
        TinyTimerModule_StartPeriodic(&st_Status.ast_SchedulerItemTimer[u8_Index],
            CoreUser_Scheduler_aScheduleTable[u8_Index].u16_IntervalSeconds * CORE_TIMEBASE_NUM_MILLISECONDS_PER_SECOND + CoreUser_Scheduler_aScheduleTable[u8_Index].u16_IntervalMilliSeconds,
            CoreUser_Scheduler_aScheduleTable[u8_Index].pfScheduledFunction,
            NULL);
    }
}

void Core_Scheduler_Execute(void)
{
    while(1)
    {
        IWDG_Refesh();
        TinyTimerModule_Run();
    }
}
