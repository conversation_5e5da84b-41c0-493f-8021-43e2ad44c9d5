/*!
 * @file
 * @brief This file contains the constant tables used for the Scheduler.
 *        This header file MUST only be included in the one file that needs access to the tables.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __COREUSER_SCHEDULER_TABLE_H__
#define __COREUSER_SCHEDULER_TABLE_H__

#include "Core_Scheduler.h"
#include "Core_CallBackTimer.h"
#include "Driver_GradualLamp.h"
#include "Driver_DoubleDamper.h"
#include "Driver_Fan.h"
#include "Driver_Flash.h"
#include "Driver_DoorSwitch.h"
#include "Driver_AdSample.h"
#include "IO_Device.h"
#include "TestUsart.h"
#include "InverterUsart.h"
#include "Parameter_TemperatureZone.h"
#include "VerticalBeamHeater.h"
#include "Iot.h"
#include "Adpt_Iwdg.h"
#include "ResolverDevice.h"
#include "FaultCode.h"
#include "HeartbeatLed.h"
#include "Driver_Emulator.h"
#include "Sbus_Core.h"
#include "Sbus_Nfc.h"
#include "Sbus_Display.h"
#include "Sbus_IceMaker.h"
#include "ParameterManager.h"
#include "FridgeRunner.h"
#include "CloudControl.h"
#include "MaintenanceManager.h"
#include "syslog.h"

/*!
 * @brief This is the scheduler table -- it contains an entry for each module's scheduler function.
 *        The entries are TSchedulerItem structure, containing an interval in milliseconds and
 *        the function to be called.
 */
static const st_CoreSchedulerItem CoreUser_Scheduler_aScheduleTable[] = {
    //{ s, ms, Pointer To Scheduled Function   }
    { 0, 0, Handle_UartTestFrame },
    { 0, 0, IWDG_Refesh },
    { 0, 10, Handle_Sbus_Frame },
    { 0, 1, Handle_Nfc_Overtime },
    { 0, 1, Handle_Display_Overtime },
    { 0, 1, Handle_IceMaker_Overtime },
    { 0, 0, Handle_UartInverterFrame },
    { 0, 10, AdSample_Handle },
    { 0, 0, IOT_Func },
    { 0, 20, Update_AllDoorsSwitchState },
    { 0, 500, Update_HeartbeatLed },
    { 1, 0, Update_TempParameter },
    { 1, 0, Handle_AllDoorsState },
    { 1, 0, Process_ResolverDeviceData },
    { 1, 0, Driver_AllFan },
    { 1, 0, Handle_DoubleDamperTimer },
    { 1, 0, Drive_VerticalBeamHeaterOutPut },
    { 1, 0, Collect_FaultCode },
    { 0, 100, UpdateSysParam },
    { 0, 500, Handle_Display_Request },
    { 0, 100, Handle_Nfc_Request },
    { 1, 0, Driver_Emulator_Run },
    { 1, 0, Update_ElectricEnergy },
    { 0, 100, ParameterManagerRun },
    { 0, 100, MaintenanceManagerRun },
    { 1, 0, CloudControlFunc },
    { 1, 0, CheckMaxStackSize }
};

#endif
